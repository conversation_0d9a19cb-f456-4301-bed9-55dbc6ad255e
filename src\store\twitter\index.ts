import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  twitterAuthApi,
  twitterCallbackApi,
  twitterStatusApi,
} from "@/services/api/twitter";
interface TwitterStore {
  isLinked: boolean;
  authToTwitter: ({ redirectUri }: { redirectUri: string }) => Promise<string>;
  callback: (code: string) => Promise<void>;
  statusToTwitter: () => Promise<boolean>;
}
const useTwitterStore = create<TwitterStore>()(
  immer((set) => ({
    isLinked: false,
    authToTwitter: async ({ redirectUri }: { redirectUri: string }) => {
      const res = await twitterAuthApi(redirectUri);
      if (res.success) {
        return res.data?.authUrl as string;
      }
      return "";
    },
    callback: async (code: string) => {
      const res = await twitterCallbackApi({ code });
      if (res.success) {
        set({ isLinked: true });
      }
    },

    statusToTwitter: async () => {
      const res = await twitterStatusApi();
      let isLoginTwitter = false;
      if (res.success) {
        isLoginTwitter = res.data?.isConnected as boolean;
      }
      return isLoginTwitter;
    },
  }))
);
export default useTwitterStore;
