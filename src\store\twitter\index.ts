import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  twitterAuthApi,
  twitterCallbackApi,
  twitterStatusApi,
} from "@/services/api/twitter";
interface TwitterStore {
  isLinked: boolean;
  auth: () => Promise<void>;
  callback: (code: string) => Promise<void>;
  statusToTwitter: () => Promise<void>;
}
const useTwitterStore = create<TwitterStore>()(
  immer((set, get) => ({
    isLinked: false,
    auth: async () => {
      const res = await twitterAuthApi();
      if (res.success) {
        window.location.href = res.data.url;
      }
    },
    callback: async (code: string) => {
      const res = await twitterCallbackApi({ code });
      if (res.success) {
        set({ isLinked: true });
      }
    },

    statusToTwitter: async () => {
      const res = await twitterStatusApi();
      if (res.success) {
        set({ isLinked: res.data.isLinked });
      }
    },
  }))
);
export default useTwitterStore;
