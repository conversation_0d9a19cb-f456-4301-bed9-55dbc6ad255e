import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  twitterAuthApi,
  twitterCallbackApi,
  twitterStatusApi,
  twitterPostApi,
} from "@/services/api/twitter";
interface TwitterStore {
  isLinked: boolean;
  authToTwitter: (taskId: string) => Promise<string>;
  callback: (code: string) => Promise<void>;
  statusToTwitter: () => Promise<boolean>;
  postToTwitter: (
    text: string,
    images: { s3Key: string }[],
    time?: number
  ) => Promise<void>;
}
const useTwitterStore = create<TwitterStore>()(
  immer((set) => ({
    isLinked: false,
    authToTwitter: async (taskId: string) => {
      const res = await twitterAuthApi(taskId);
      if (res.success) {
        return res.data?.authUrl as string;
      }
      return "";
    },
    callback: async (code: string) => {
      const res = await twitterCallbackApi({ code });
      if (res.success) {
        set({ isLinked: true });
      }
    },

    statusToTwitter: async () => {
      const res = await twitterStatusApi();
      let isLoginTwitter = false;
      if (res.success) {
        isLoginTwitter =
          (res.data?.isConnected as boolean) && !res.data?.isExpired;
      }
      return isLoginTwitter;
    },
    postToTwitter: async (
      text: string,
      images: { s3Key: string }[],
      time?: number
    ) => {
      await twitterPostApi({ text, images, time });
    },
  }))
);
export default useTwitterStore;
