import React from "react";
import { Progress } from "@/components/ui/progress";
import { useLanguage } from "@/hooks/useLanguage";
import type { ProgressBarProps, FileData } from "../types";

interface EnhancedProgressBarProps extends ProgressBarProps {
  file?: FileData;
}

export const ProgressBar = ({ file }: EnhancedProgressBarProps) => {
  const { t } = useLanguage();

  // 获取进度数据，优先使用传入的file数据
  const progress = file?.progress || 0;
  const status = file?.status || "pending";
  const uploadSpeed = file?.uploadSpeed;
  const remainingTime = file?.remainingTime;

  return (
    <div className="mt-3">
      <div className="flex justify-between text-sm text-text-secondary mb-1">
        <span>{t("upload:progress.title")}</span>
        <span>{progress}%</span>
      </div>

      <Progress value={progress} className="h-2 mb-2" />

      {/* 详细信息 */}
      <div className="flex justify-between text-xs text-text-tertiary">
        <div className="flex items-center gap-2">
          {status === "success" && (
            <span className="text-success">
              ✓ {t("upload:progress.complete")}
            </span>
          )}
          {status === "error" && (
            <span className="text-destructive">
              ✗ {t("upload:progress.failed")}
            </span>
          )}
          {status === "uploading" && uploadSpeed && (
            <span>
              {t("upload:progress.speed")}: {uploadSpeed}
            </span>
          )}
        </div>

        <div>
          {status === "uploading" && remainingTime && (
            <span>
              {t("upload:progress.remaining")}: {remainingTime}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
