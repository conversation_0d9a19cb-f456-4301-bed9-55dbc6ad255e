"use client";

import { PlatformContentRendererProps, PlatformType } from "../types";
import Twitter from "./Twitter";
import Xhs from "./Xhs";

/**
 * 平台内容渲染器组件
 * 根据当前语言选择合适的平台组件进行渲染
 */
const PlatformContentRenderer = ({
  data,
  currentLanguage,
  onPublish,
  onSave,
}: PlatformContentRendererProps) => {
  // 根据语言选择平台组件
  const renderPlatformComponent = () => {
    switch (currentLanguage) {
      case PlatformType.XHS:
        return <Xhs data={data} onPublish={onPublish} onSave={onSave} />;

      case PlatformType.INSTAGRAM:
        // 未来可以添加 Instagram 组件
        return <Twitter data={data} onPublish={onPublish} onSave={onSave} />;

      case PlatformType.TWITTER:
      default:
        return <Twitter data={data} onPublish={onPublish} onSave={onSave} />;
    }
  };

  return renderPlatformComponent();
};

export default PlatformContentRenderer;
