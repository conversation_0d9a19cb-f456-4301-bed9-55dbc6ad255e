"use client";

import { User<PERSON>roupIcon, PlusIcon } from "@heroicons/react/24/solid";
import * as React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { useSidebar } from "@/components/ui/sidebar";
import { useLanguage } from "@/hooks/useLanguage";
import ModalAddRole from "../modal/ModalAddRole";
import RoleList from "../role-list";

export function TeamSwitcher() {
  const { t } = useLanguage();
  const [open, setOpen] = React.useState(false);
  const { state } = useSidebar();

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div className="app-logo">
          <div className="logo-container">
            <div className="persona-icon">
              <div className="persona-layers">
                <div className="persona-layer layer-1"></div>
                <div className="persona-layer layer-2"></div>
                <div className="persona-layer layer-3"></div>
              </div>
            </div>
            {state === "collapsed" ? null : (
              <span className="logo-text">PersonaRoll</span>
            )}
          </div>
        </div>
      </SidebarMenuItem>
      <SidebarMenuItem className="text-text-secondary hover:text-text-primary h-14">
        <SidebarMenuButton
          onClick={() => setOpen(true)}
          tooltip={t("route:createIdentity")}
          className="flex items-center justify-between h-full"
        >
          <div className="flex items-center gap-2 shrink-0 h-full">
            <UserGroupIcon className="size-5 shrink-0"></UserGroupIcon>
            {t("route:createIdentity")}
          </div>
          <PlusIcon className="!w-5 !h-5 shrink-0"></PlusIcon>
        </SidebarMenuButton>
      </SidebarMenuItem>
      <SidebarMenuItem>
        <ScrollArea className="h-56">
          <RoleList onCreateRole={() => setOpen(true)}></RoleList>
        </ScrollArea>
      </SidebarMenuItem>
      <ModalAddRole open={open} close={() => setOpen(false)}></ModalAddRole>
    </SidebarMenu>
  );
}
