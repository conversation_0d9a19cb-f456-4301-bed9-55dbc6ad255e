import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";
export enum TaskStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}
export enum ProcessingStage {
  INIT = "INIT",
  PHOTO_ANALYSIS = "PHOTO_ANALYSIS",
  KEYWORD_GENERATION = "KEYWORD_GENERATION",
  VECTOR_SEARCH = "VECTOR_SEARCH",
  MCP_FETCH = "MCP_FETCH",
  CONTENT_GENERATION = "CONTENT_GENERATION",
  FINAL_GENERATION = "FINAL_GENERATION",
}

export interface IGenerationTask {
  taskId: string; // 主键 - 任务唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  status: TaskStatus; // 任务状态
  stage: ProcessingStage; // 当前处理阶段
  selectedPhotos: string[]; // 选中的照片ID列表
  searchKeywords: string[]; // AI生成的搜索关键词
  topNResults: any[]; // 向量搜索结果
  mcpData: any[]; // 外部MCP数据

  candidateContent: any[]; // 候选内容
  finalContent: string; // 最终生成内容
  errorMessage?: string; // 错误信息
  progress: number; // 进度百分比 0-100
  estimatedTimeRemaining?: number; // 预估剩余时间(秒)
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}
// generate
export const generateApi = async (
  personaId: string,
  connectionId: string
): Promise<ApiResponse<{ message: string; taskId: string }>> => {
  return await api.post(`/ai/generate`, { personaId, connectionId });
};
export interface IGenerateList {
  id?: string;
  userId?: string;
  personaId?: string;
  finalContent: string;
  title?: string;
  tags?: string[];
  style?: string;
  reasoning?: string;
  photos?: {
    s3Key: string;
    imageUrl: string;
    id: string;
  }[];
  createdAt?: string;
  updatedAt?: string;
  taskId?: string;
  status?: string;
}
// result
export const generateResultApi = async (
  taskId: string
): Promise<
  ApiResponse<{
    finalContent: IGenerateList[];
  }>
> => {
  return await api.post(`/ai/generateResult`, { taskId });
};

//generateGetList

export const generateGetListApi = async (
  personaId: string
): Promise<
  ApiResponse<{
    generateLists: IGenerateList[];
  }>
> => {
  return await api.post(`/ai/generateGetList`, { personaId });
};

// generateUpdate
export const generateUpdateApi = async (
  data: IGenerateList,
  status: string,
  taskId: string,
  personaId: string
): Promise<ApiResponse<{ success: boolean }>> => {
  return await api.post(`/ai/generateUpdate`, {
    data,
    status,
    taskId,
    personaId,
  });
};

//generateGetGenerate
export const generateGetGenerateApi = async (
  taskId: string
): Promise<
  ApiResponse<{
    generate: IGenerateList;
  }>
> => {
  return await api.post(`/ai/generateGetGenerate`, { taskId });
};

// generateStatistics
export const generateStatisticsApi = async (
  personaId: string
): Promise<
  ApiResponse<{
    statistics: {
      totalGenerated: number; // 总共生成的内容条数（模型生成的条数）
      published: number; // 发布条数
      favorites: number; // 保存条数（收藏）
      draft: number; // 草稿条数（初始状态）
      deleted: number; // 已删除条数
    };
  }>
> => {
  return await api.post(`/ai/generateStatistics`, { personaId });
};
