"use client";

import { PhotoIcon } from "@heroicons/react/24/solid";
import { useEffect, useMemo } from "react";
import { Card } from "@/components/business/card";
import { Button } from "@/components/ui/button";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";
import usePhotoStore from "@/store/photo";
import UsageProgress from "./UsageProgress";

interface PhotoUsageCardProps {
  title: string;
  onManageClick?: () => void;
  buttonText?: string;
}

const PhotoUsageCard = ({
  title,
  onManageClick,
  buttonText,
}: PhotoUsageCardProps) => {
  const { t } = useLanguage();
  const { currentRole } = useRoleStore();
  const { statistics, getPhotoStatistics } = usePhotoStore();

  const totalCount = useMemo(() => {
    return statistics.find((item) => item.type === "total")?.count || 0;
  }, [statistics]);
  const usedCount = useMemo(() => {
    return statistics.find((item) => item.type === "used")?.count || 0;
  }, [statistics]);

  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getPhotoStatistics(currentRole.personaId);
    }
  }, [currentRole, getPhotoStatistics]);

  return (
    <Card>
      <Text variant={TextVariant.H4} className="mb-4">
        {title}
      </Text>
      <UsageProgress used={usedCount} total={totalCount} />
      <Button className="mt-4" onClick={onManageClick}>
        <PhotoIcon className="size-4" />
        {buttonText || t("button:managePhotoLibrary")}
      </Button>
    </Card>
  );
};

export default PhotoUsageCard;
