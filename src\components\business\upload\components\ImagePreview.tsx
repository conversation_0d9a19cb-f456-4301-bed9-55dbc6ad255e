import { Download, Trash2, <PERSON>, <PERSON>ader2 } from "lucide-react";
import { useState } from "react";
import type { FilePreviewProps } from "../types";
import { formatFileSize } from "../utils/fileUtils";
import { ErrorMessage } from "./ErrorMessage";
import { ImageModal } from "./ImageModal";
import { ProgressBar } from "./ProgressBar";
import { StatusBadge } from "./StatusBadge";
import { StatusIcon } from "./StatusIcon";

export const ImagePreview = ({
  file,
  onRemove,
  isDeleting = false,
}: FilePreviewProps) => {
  const [showPreview, setShowPreview] = useState(false);

  return (
    <div className="bg-surface rounded-lg p-4 border border-border">
      <div className="flex items-start space-x-4">
        {/* 缩略图 */}
        <div className="relative">
          <img
            src={file.preview}
            alt={file.name}
            className="w-16 h-16 object-cover rounded-lg cursor-pointer"
            onClick={() => setShowPreview(true)}
          />
          <button
            onClick={() => setShowPreview(true)}
            className="absolute inset-0 bg-black-alpha-80 opacity-0 hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center"
          >
            <Eye className="w-4 h-4 text-white" />
          </button>
        </div>

        {/* 文件信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium text-text-primary truncate">
              {file.name}
            </h3>
            <StatusIcon status={file.status} />
          </div>
          <div className="flex items-center space-x-4 mt-1 text-sm text-text-secondary">
            <span>{formatFileSize(file.size)}</span>
            <span>{file.uploadTime}</span>
            <StatusBadge status={file.status} />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          {file.status === "success" && (
            <button className="p-2 text-text-secondary hover:text-text-primary transition-colors">
              <Download className="w-4 h-4" />
            </button>
          )}
          <button
            onClick={() => onRemove(file.id)}
            disabled={isDeleting}
            className="p-2 text-text-secondary hover:text-error transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeleting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* 上传进度：上传中、成功、失败时都显示 */}
      {(file.status === "uploading" ||
        file.status === "success" ||
        file.status === "error") && (
        <ProgressBar fileId={file.id} file={file} />
      )}

      {/* 错误信息 */}
      {file.errors.length > 0 && <ErrorMessage errors={file.errors} />}

      {/* 预览模态框 */}
      {showPreview && file.preview && (
        <ImageModal
          src={file.preview}
          alt={file.name}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
};
