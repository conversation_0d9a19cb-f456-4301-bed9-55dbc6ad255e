"use client";

import { PaintBrushIcon } from "@heroicons/react/24/solid";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { PreloadWrapper } from "@/components/PreloadWrapper";
import { But<PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import ContentPhotoStatusSkeleton from "@/pages-components/home/<USER>/Skeleton";
import RecentContentSkeleton from "@/pages-components/home/<USER>/Skeleton";
import UserInfoSkeleton from "@/pages-components/home/<USER>/Skeleton";
import { useAppStore } from "@/store/useAppStore";

// 懒加载组件
const UserInfo = dynamic(() => import("@/pages-components/home/<USER>"), {
  loading: () => <UserInfoSkeleton />,
});

const ContentPhotoStatus = dynamic(
  () => import("@/pages-components/home/<USER>"),
  {
    loading: () => <ContentPhotoStatusSkeleton />,
  }
);

const RecentContent = dynamic(
  () => import("@/pages-components/home/<USER>"),
  {
    loading: () => <RecentContentSkeleton />,
  }
);

export default function HomePage() {
  const { t } = useLanguage();
  const { setHeaderSlot } = useAppStore();
  const router = useRouter();

  useEffect(() => {
    setHeaderSlot(
      <Button
        onClick={() => {
          router.push("/workspace");
        }}
      >
        <PaintBrushIcon className="size-4"></PaintBrushIcon>
        {t("button:startCreating")}
      </Button>
    );

    // 清理函数：组件卸载时清除 slot
    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot]);

  return (
    <PreloadWrapper>
      <div className="grid gap-4">
        <UserInfo />
        <ContentPhotoStatus />
        <RecentContent />
      </div>
    </PreloadWrapper>
  );
}
