import clsx from "clsx";
import * as React from "react";
import {
  Select as SelectPrimitive,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
export function Select({
  placeholder,
  options,
  className,
  id,
  onValueChange,
  value,
}: {
  placeholder?: string;
  options: { name: string; value: string }[];
  className?: string;
  id?: string;
  value?: string;
  onValueChange?: (value: string) => void;
}) {
  return (
    <SelectPrimitive onValueChange={onValueChange} value={value}>
      <SelectTrigger id={id} className={clsx("!h-12", className)}>
        <SelectValue className="h-full" placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {options.map((o) => {
            return (
              <SelectItem key={o.value} value={o.value}>
                {o.name}
              </SelectItem>
            );
          })}
        </SelectGroup>
      </SelectContent>
    </SelectPrimitive>
  );
}
