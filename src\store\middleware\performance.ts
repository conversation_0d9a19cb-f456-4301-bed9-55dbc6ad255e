import { StateCreator } from "zustand";

// 性能监控中间件
export const performanceMiddleware =
  <T>(f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const wrappedSet: typeof set = (
      partial: T | Partial<T> | ((state: T) => T | Partial<T>),
      replace?: boolean
    ) => {
      const start = performance.now();

      // 执行状态更新
      // 只允许 replace 为 true 时传 true，否则传 false 或 undefined
      if (replace === true) {
        set(partial as T | ((state: T) => T), true);
      } else {
        set(partial as T | Partial<T> | ((state: T) => T | Partial<T>), false);
      }

      const end = performance.now();
      const duration = end - start;

      // 如果状态更新耗时过长，发出警告
      if (duration > 10 && process.env.NODE_ENV === "development") {
        console.warn(`Slow state update detected: ${duration.toFixed(2)}ms`);
      }
    };

    return f(wrappedSet, get, api);
  };

// 日志中间件
export const loggerMiddleware =
  <T>(f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const wrappedSet: typeof set = (
      partial: T | Partial<T> | ((state: T) => T | Partial<T>),
      replace?: boolean
    ) => {
      if (process.env.NODE_ENV === "development") {
        const prevState = get();
        // Type-safe call to set
        if (replace === true) {
          const result = set(partial as T | ((state: T) => T), true);
          const nextState = get();

          console.group("🏪 Store Update");
          console.log("Previous State:", prevState);
          console.log("Next State:", nextState);
          console.groupEnd();

          return result;
        } else {
          const result = set(
            partial as T | Partial<T> | ((state: T) => T | Partial<T>),
            false
          );
          const nextState = get();

          console.group("🏪 Store Update");
          console.log("Previous State:", prevState);
          console.log("Next State:", nextState);
          console.groupEnd();

          return result;
        }
      }

      if (replace === true) {
        return set(partial as T | ((state: T) => T), true);
      } else {
        return set(
          partial as T | Partial<T> | ((state: T) => T | Partial<T>),
          false
        );
      }
    };

    return f(wrappedSet, get, api);
  };
