{"generate": {"tip": "Enter a theme or let AI recommend, generate with one click"}, "howToUse": {"title": "🚀 How to Use", "expand": "Expand", "collapse": "Collapse", "steps": {"uploadPhoto": "📸 Upload Photos", "uploadPhotoDescription": "Go to Camera Roll to upload your photos, AI will generate relevant content based on the photo content", "selectTheme": "🎯 Select Theme", "selectThemeDescription": "Choose an interesting theme from creation suggestions, or customize a theme in the input box", "generateContent": "✨ One-Click Generate", "generateContentDescription": "Click the generate button, and AI will automatically create complete Xiaohongshu content"}}, "writingAssistant": {"title": "💡 Creation Suggestions", "tip": "Click to select a theme, generate content with one click"}, "generationLog": {"title": "📝 Generation History", "tip": "View generation records, copy content with one click", "usePhoto": "Use Photos: ", "viewDetail": "View Details"}, "generated": {"title": "🎉 Creation Complete! Generated {{num}} {{platform}} posts for you", "tip": "Click on a post to edit content, publish directly to {{platform}} when satisfied", "xhs": "📱 <PERSON><PERSON><PERSON>", "instagram": "📷 Instagram", "twitter": "🐦 Twitter"}, "generating": {"steps": {"userAnalysis": {"title": "User Input Analysis...", "details": {"currentPersona": "Current Persona: {{personaName}}", "personaDescription": "Content creation needs to match the persona's characteristics. {{personaName}} {{personalityTraits}}, expert topics: {{expertTopics}}"}}, "knowledgeSearch": {"title": "Knowledge Base Search in Progress...", "details": {"searchingDatabase": "Calling Xiaohongshu content database, searching for relevant high-engagement content...", "analyzingFiles": "Analyzing user-uploaded knowledge base files:"}}, "contentGeneration": {"title": "Content Generation in Progress...", "details": {"qualityCheck": "Quality Check Results:", "communityCompliance": "✓ Content complies with Xiaohongshu community guidelines", "styleMatch": "✓ Language style matches {{personaName}}'s persona", "tagUsage": "✓ Topic tags are used appropriately, expected to have good exposure", "structureClear": "✓ Content structure is clear and easy to read", "postsReady": "{{postCount}} high-quality Xiaohongshu posts are ready!"}}}}, "notStarted": "Not Started", "generators": "Generating...", "completed": "Completed"}