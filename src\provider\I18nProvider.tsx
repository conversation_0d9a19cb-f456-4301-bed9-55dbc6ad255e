"use client";

import { useEffect, useState } from "react";
import { I18nextProvider } from "react-i18next";
import { PageLoading } from "@/components/ui/Loading";
import i18n, { initializeI18nWithStore } from "@/lib/i18n";
import useConfigStore from "@/store/useConfig";

interface I18nProviderProps {
  children: React.ReactNode;
}

export default function I18nProvider({ children }: I18nProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const { language } = useConfigStore();

  useEffect(() => {
    // 使用 store 中的语言初始化 i18n
    const initializeWithStoreLanguage = async () => {
      await initializeI18nWithStore(language);
      setIsInitialized(true);
    };

    // 确保 i18n 已经初始化
    if (i18n.isInitialized) {
      // 如果已经初始化，但语言发生了变化，重新初始化
      if (i18n.language !== language) {
        initializeWithStoreLanguage();
      } else {
        setIsInitialized(true);
      }
    } else {
      // 如果还没初始化，使用 store 语言初始化
      initializeWithStoreLanguage();
    }
  }, [language]);

  // 在 i18n 初始化完成前显示加载状态
  if (!isInitialized) {
    return <PageLoading></PageLoading>;
  }

  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>;
}
