"use client";

import { useCallback, useEffect, useState } from "react";
import { DialogModal } from "@/components/business/modal";
import { Select } from "@/components/business/select";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useTwitterStore from "@/store/twitter";

// Twitter/X 图标组件
const TwitterIcon = ({ className }: { className?: string }) => (
  <svg
    viewBox="0 0 24 24"
    className={className}
    fill="currentColor"
    aria-hidden="true"
  >
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// 盾牌图标组件
const ShieldIcon = ({ className }: { className?: string }) => (
  <svg
    viewBox="0 0 24 24"
    className={className}
    fill="currentColor"
    aria-hidden="true"
  >
    <path
      fillRule="evenodd"
      d="M12.516 2.17a.75.75 0 00-1.032 0 11.209 11.209 0 01-7.877 ********** 0 00-.722.515A12.74 12.74 0 002.25 9.75c0 5.814 3.051 10.77 7.608 13.566a.75.75 0 00.284 0C14.699 20.52 17.75 15.564 17.75 9.75a12.74 12.74 0 00-.635-*********** 0 00-.722-.515 11.209 11.209 0 01-7.877-3.08z"
      clipRule="evenodd"
    />
  </svg>
);

// 连接状态枚举
enum ConnectionState {
  NOT_CONNECTED = "not_connected",
  CONNECTED = "connected",
}

interface TwitterConnectModalProps {
  open: boolean;
  onClose: () => void;
  onConnect?: () => void;
  onPublish?: () => void;
  twitterContent: {
    data: IGenerateList;
    taskId: string;
    personaId: string;
  } | null;
}

export default function TwitterConnectModal({
  open,
  onClose,
  onPublish,
  twitterContent,
}: TwitterConnectModalProps) {
  const { t } = useLanguage();
  const { authToTwitter, statusToTwitter, postToTwitter } = useTwitterStore();
  const [connectionState, setConnectionState] = useState<ConnectionState>(
    ConnectionState.NOT_CONNECTED
  );
  const [isLoading, setIsLoading] = useState(false);
  const [publishOption, setPublishOption] = useState<"now" | "auto">("now");
  const [autoPostInterval, setAutoPostInterval] = useState(1);

  // 模拟的用户数据 - 实际项目中应该从API获取
  const [twitterUser] = useState({
    username: "",
    // followers: "1.2K",
  });

  const checkConnectionStatus = useCallback(async () => {
    try {
      const isConnected = await statusToTwitter();
      setConnectionState(
        isConnected ? ConnectionState.CONNECTED : ConnectionState.NOT_CONNECTED
      );
    } catch (error) {
      console.error("检查连接状态失败:", error);
      setConnectionState(ConnectionState.NOT_CONNECTED);
    }
  }, [statusToTwitter]);

  useEffect(() => {
    if (open) {
      checkConnectionStatus();
    }
  }, [open]);

  const handleConnect = async () => {
    if (connectionState === ConnectionState.CONNECTED) {
      handleDisconnect();
      return;
    }
    try {
      setIsLoading(true);
      const authUrl = await authToTwitter(twitterContent?.taskId as string);
      if (authUrl) {
        window.open(authUrl, "_self");
      }
    } catch (error) {
      console.error("Twitter连接失败:", error);
      setConnectionState(ConnectionState.NOT_CONNECTED);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = () => {
    setConnectionState(ConnectionState.NOT_CONNECTED);
  };

  const handlePublish = async () => {
    try {
      setIsLoading(true);
      // 这里调用发布API
      await postToTwitter(
        twitterContent?.data.finalContent as string,
        twitterContent?.data.photos?.map((item) => ({
          s3Key: item.s3Key,
        })) as {
          s3Key: string;
        }[]
      );
      onPublish?.();
      onClose();
    } catch (error) {
      console.error("发布失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DialogModal
      open={open}
      openChange={onClose}
      className="w-[32rem] bg-surface border-border"
      title={t("modal:publishToTwitter.title")}
    >
      <div className="space-y-6">
        {/* 发布说明 */}
        <div className="text-text-secondary">
          {t("modal:publishToTwitter.publishTo")}
        </div>

        {/* 已连接账户信息 */}
        <div className="space-y-4">
          <h3 className="text-text-primary font-medium">
            {t("modal:publishToTwitter.connectedAccount")}
          </h3>

          <div className="flex items-center justify-between p-4 bg-surface-light rounded-lg border border-border">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <TwitterIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-text-primary font-medium">
                  {twitterUser.username ? (
                    `@${twitterUser.username}`
                  ) : (
                    <span className="text-text-secondary">
                      {t("modal:connectToTwitter.notConnect")}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleConnect}
              className="text-text-secondary hover:text-text-primary"
            >
              {connectionState === ConnectionState.CONNECTED ? (
                <>
                  <ShieldIcon className="w-4 h-4" />
                  <span className="ml-2">
                    {t("modal:publishToTwitter.disconnect")}
                  </span>
                </>
              ) : (
                <>
                  {connectionState === ConnectionState.NOT_CONNECTED
                    ? t("modal:connectToTwitter.authorize")
                    : ""}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 发布选项 */}
        <div className="space-y-4">
          <h3 className="text-text-primary font-medium">
            {t("modal:publishToTwitter.publishingOptions")}
          </h3>

          <div className="space-y-3">
            {/* 立即发布选项 */}
            <div
              className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                publishOption === "now"
                  ? "border-blue-500 bg-blue-500/10"
                  : "border-border bg-surface-light hover:border-border-light"
              }`}
              onClick={() => setPublishOption("now")}
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    publishOption === "now"
                      ? "border-blue-500 bg-blue-500"
                      : "border-border"
                  }`}
                >
                  {publishOption === "now" && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <span className="text-text-primary font-medium">
                  {t("modal:publishToTwitter.postNow")}
                </span>
              </div>
            </div>

            {/* 自动发布选项 */}
            <div
              className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                publishOption === "auto"
                  ? "border-blue-500 bg-blue-500/10"
                  : "border-border bg-surface-light hover:border-border-light"
              }`}
              onClick={() => setPublishOption("auto")}
            >
              <div className="flex items-center gap-3">
                <div
                  className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    publishOption === "auto"
                      ? "border-blue-500 bg-blue-500"
                      : "border-border"
                  }`}
                >
                  {publishOption === "auto" && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <span className="text-text-primary font-medium">
                  {t("modal:publishToTwitter.autoPost")}
                  <Select
                    options={Array.from({ length: 24 }, (_, i) => ({
                      name: `${i + 1} ${t("modal:publishToTwitter.hours")}`,
                      value: (i + 1).toString(),
                    }))}
                    value={publishOption === "auto" ? "1" : ""}
                    onValueChange={() => {}}
                  ></Select>
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-6 border-border text-text-secondary hover:text-text-primary"
          >
            {t("modal:publishToPlatform.cancel")}
          </Button>

          <Button
            onClick={handlePublish}
            disabled={
              isLoading || connectionState !== ConnectionState.CONNECTED
            }
            className="px-6 bg-blue-500 hover:bg-blue-600 text-white"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>{t("modal:publishToTwitter.publishing")}</span>
              </div>
            ) : (
              t("modal:publishToTwitter.publish")
            )}
          </Button>
        </div>
      </div>
    </DialogModal>
  );
}
