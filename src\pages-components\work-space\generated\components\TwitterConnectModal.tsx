"use client";

import { useEffect, useState } from "react";
import { DialogModal } from "@/components/business/modal";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import useTwitterStore from "@/store/twitter";

// Twitter/X 图标组件
const TwitterIcon = ({ className }: { className?: string }) => (
  <svg
    viewBox="0 0 24 24"
    className={className}
    fill="currentColor"
    aria-hidden="true"
  >
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

interface TwitterConnectModalProps {
  open: boolean;
  onClose: () => void;
  onConnect?: () => void;
}

export default function TwitterConnectModal({
  open,
  onClose,
  onConnect,
}: TwitterConnectModalProps) {
  const { t } = useLanguage();
  const { authToTwitter, statusToTwitter } = useTwitterStore();
  const [isConnecting, setIsConnecting] = useState(false);
  useEffect(() => {
    const isConnecting = statusToTwitter();
    setIsConnecting(isConnecting);
  }, [open]);

  const handleConnect = async () => {
    try {
      setIsConnecting(true);

      // 获取当前页面的URL作为重定向URI
      const redirectUri = `${window.location.origin}/auth/twitter/callback`;

      // 调用Twitter认证API
      const authUrl = await authToTwitter({ redirectUri });

      if (authUrl) {
        // 跳转到Twitter认证页面
        window.location.href = authUrl;
        onConnect?.();
      }
    } catch (error) {
      console.error("Twitter连接失败:", error);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <DialogModal
      open={open}
      openChange={onClose}
      className="w-[32rem] bg-surface border-border"
      title={
        <div className="flex items-center gap-2">
          <span className="text-2xl">📱</span>
          <span>{t("modal:publishToPlatform.title")}</span>
        </div>
      }
    >
      <div className="space-y-6">
        {/* 连接说明 */}
        <div className="text-text-secondary">
          {t("modal:publishToPlatform.connectWithTwitter")}
        </div>

        {/* Twitter连接卡片 */}
        <div className="relative">
          {/* 蓝色边框容器 */}
          <div className="border-2 border-blue-500 rounded-xl p-6 bg-surface-light">
            {/* Twitter图标和标题 */}
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center">
                <TwitterIcon className="w-8 h-8 text-white" />
              </div>

              <div className="text-center">
                <h3 className="text-white font-medium text-lg">
                  {t("modal:publishToPlatform.twitterX")}
                </h3>
              </div>

              {/* 连接按钮 */}
              <Button
                onClick={handleConnect}
                disabled={isConnecting}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white border-0 h-11 rounded-lg font-medium"
              >
                {isConnecting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>连接中...</span>
                  </div>
                ) : (
                  t("modal:publishToPlatform.connectAccount")
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-6 border-border text-text-secondary hover:text-text-primary"
          >
            {t("modal:publishToPlatform.cancel")}
          </Button>

          <Button
            onClick={handleConnect}
            disabled={isConnecting}
            className="px-6 bg-blue-500 hover:bg-blue-600 text-white"
          >
            {isConnecting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>连接中...</span>
              </div>
            ) : (
              t("modal:publishToPlatform.connectWithX")
            )}
          </Button>
        </div>
      </div>
    </DialogModal>
  );
}
