"use client";
import { Card } from "@/components/business/card";
import { Badge } from "@/components/ui/badge";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";

const assistant = [
  {
    name: "2025年春季穿搭灵感：复古风回潮",
    description: "复古元素强势回归，千禧风格与Y2K美学融合，打造独特个人风格...",
    type: "hot",
    sourcePlatform: "📱小红书热门话题",
    id: "1",
  },
  {
    name: "2025年春季穿搭灵感：复古风回潮",
    description: "复古元素强势回归，千禧风格与Y2K美学融合，打造独特个人风格...",
    type: "hot",
    sourcePlatform: "📱小红书热门话题",
    id: "2",
  },
  {
    name: "2025年春季穿搭灵感：复古风回潮",
    description: "复古元素强势回归，千禧风格与Y2K美学融合，打造独特个人风格...",
    type: "hot",
    sourcePlatform: "📱小红书热门话题",
    id: "3",
  },
];
const WritingAssistant = () => {
  const { t } = useLanguage();

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("workspace:writingAssistant.title")}
        <span className="text-[13px] text-text-tertiary">
          {t("workspace:writingAssistant.tip")}
        </span>
      </Text>
      <div className="grid gap-4 grid-cols-2 max-md:grid-cols-1">
        {assistant.map((item) => (
          <Card
            key={item.id}
            className="bg-surface-light hover:border-ring hover:shadow-glow-lg"
          >
            <div>
              <Text
                variant={TextVariant.BODY_MEDIUM}
                className="flex justify-between items-center mb-5"
              >
                {item.name}
                <Badge variant="outline">{item.type}</Badge>
              </Text>
              <Text
                variant={TextVariant.CAPTION}
                className="text-text-secondary line-clamp-2"
              >
                {item.description}
              </Text>
              <Text
                variant={TextVariant.CAPTION}
                className="text-text-tertiary mt-4 inline-block"
              >
                {item.sourcePlatform}
              </Text>
            </div>
          </Card>
        ))}
      </div>
    </Card>
  );
};

export default WritingAssistant;
