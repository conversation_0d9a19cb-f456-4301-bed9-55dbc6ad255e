"use client";

import {
  InboxArrowDownIcon,
  TruckIcon,
  ServerStackIcon,
} from "@heroicons/react/24/outline";
import { Card } from "@/components/business/card";
import { But<PERSON> } from "@/components/ui/button";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";

const DataSettings = () => {
  const { t } = useLanguage();

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("setting:data.title")}
      </Text>
      <div className="flex flex-col gap-4">
        <Button variant="secondary" className="justify-start">
          <InboxArrowDownIcon className="size-4"></InboxArrowDownIcon>{" "}
          {t("button:exportData")}
        </Button>
        <Button variant="secondary" className="justify-start">
          <TruckIcon className="size-4"></TruckIcon> {t("button:clearCache")}
        </Button>
        <Button variant="secondary" className="justify-start">
          <ServerStackIcon className="size-4"></ServerStackIcon>
          {t("button:backupData")}
        </Button>
      </div>
    </Card>
  );
};

export default DataSettings;
