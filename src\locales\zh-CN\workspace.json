{"generate": {"tip": "输入主题或让AI智能推荐，一键生成"}, "howToUse": {"title": "🚀 如何使用", "expand": "展开", "collapse": "收起", "steps": {"uploadPhoto": "📸 上传照片", "uploadPhotoDescription": "前往Camera Roll上传你的照片，AI会基于照片内容生成相关文案", "selectTheme": "🎯 选择主题", "selectThemeDescription": "从创作建议中选择感兴趣的主题，或在输入框中自定义主题", "generateContent": "✨ 一键生成", "generateContentDescription": "点击一键生成按钮，AI会自动生成完整的小红书内容"}}, "writingAssistant": {"title": "💡 创作建议", "tip": "点击选择主题，一键生成内容"}, "generationLog": {"title": "📝 生成历史", "tip": "查看生成记录，一键复制内容", "usePhoto": "使用照片: ", "viewDetail": "查看详情"}, "generated": {"title": "🎉 创作完成！为你生成了{{num}}篇{{platform}}帖子", "tip": "点击帖子可以编辑内容，满意后可以直接发布到{{platform}}", "xhs": "📱小红书", "instagram": "📷 Instagram", "twitter": "🐦Twitter"}, "generating": {"steps": {"userAnalysis": {"title": "用户输入分析...", "details": {"currentPersona": "当前人设：{{personaName}}", "personaDescription": "内容创作需要与该人设的特点一致。{{personaName}}{{personalityTraits}}，擅长话题：{{expertTopics}}"}}, "knowledgeSearch": {"title": "知识库搜索进行中...", "details": {"searchingDatabase": "正在调用小红书内容数据库，搜索相关的高互动内容...", "analyzingFiles": "正在分析用户上传的知识库文件："}}, "contentGeneration": {"title": "内容生成...", "details": {"qualityCheck": "质量检查结果：", "communityCompliance": "✓ 内容符合小红书社区规范", "styleMatch": "✓ 语言风格符合{{personaName}}的人设", "tagUsage": "✓ 话题标签使用得当，预计会有良好的曝光效果", "structureClear": "✓ 内容结构清晰，易于阅读", "postsReady": "{{postCount}} 条高质量的小红书帖子已准备好！"}}}}, "notStarted": "未开始", "generators": "生成中...", "completed": "已完成"}