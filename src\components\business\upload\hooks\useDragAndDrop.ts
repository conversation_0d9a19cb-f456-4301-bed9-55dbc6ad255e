import { useState, useCallback, useRef } from "react";
import { getFileCategory } from "../utils/fileUtils";

export const useDragAndDrop = (
  uploading: boolean,
  onFilesDropped: (files: FileList) => void
) => {
  const [isDragActive, setIsDragActive] = useState(false);
  const [isDragAccept, setIsDragAccept] = useState(false);
  const [isDragReject, setIsDragReject] = useState(false);
  const dragCounter = useRef(0);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    dragCounter.current++;

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragActive(true);

      const hasValidFiles = Array.from(e.dataTransfer.items).some(
        (item: DataTransferItem) => {
          return item.kind === "file" && getFileCategory({ type: item.type });
        }
      );

      setIsDragAccept(hasValidFiles);
      setIsDragReject(!hasValidFiles);
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    dragCounter.current--;

    if (dragCounter.current === 0) {
      setIsDragActive(false);
      setIsDragAccept(false);
      setIsDragReject(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();

      setIsDragActive(false);
      setIsDragAccept(false);
      setIsDragReject(false);
      dragCounter.current = 0;

      if (uploading) {
        return;
      }

      const droppedFiles = e.dataTransfer.files;
      onFilesDropped(droppedFiles);
    },
    [uploading, onFilesDropped]
  );

  return {
    isDragActive,
    isDragAccept,
    isDragReject,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
  };
};
