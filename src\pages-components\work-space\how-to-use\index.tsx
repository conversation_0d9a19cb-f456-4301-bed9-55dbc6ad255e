"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/business/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";

const HowToUse = () => {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const steps = useMemo(() => {
    return [
      {
        name: t("workspace:howToUse.steps.uploadPhoto"),
        description: t("workspace:howToUse.steps.uploadPhotoDescription"),
      },
      // {
      //   name: t("workspace:howToUse.steps.selectTheme"),
      //   description: t("workspace:howToUse.steps.selectThemeDescription"),
      // },
      {
        name: t("workspace:howToUse.steps.generateContent"),
        description: t("workspace:howToUse.steps.generateContentDescription"),
      },
    ];
  }, [t]);

  return (
    <Card>
      <Accordion
        type="single"
        collapsible
        onValueChange={(value) => {
          setOpen(value === "item-1");
        }}
        className="w-full"
      >
        <AccordionItem value="item-1">
          <AccordionTrigger className="text-lg flex items-center">
            <div className="w-full flex justify-between items-center">
              <span>{t("workspace:howToUse.title")}</span>
              <span className="text-[12px] text-text-tertiary -mr-2 translate-y-0.5">
                {open
                  ? t("workspace:howToUse.collapse")
                  : t("workspace:howToUse.expand")}
              </span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="grid grid-cols-2 max-md:grid-cols-1 gap-4">
            {steps.map((step, index) => (
              <Card key={step.name} className="bg-surface-light">
                <div className="flex gap-4 items-start">
                  <div className="w-8 h-8 rounded-full shrink-0 bg-white-alpha-10 flex items-center justify-center text-text-secondary">
                    {index + 1}
                  </div>
                  <div>
                    <Text variant={TextVariant.BODY_MEDIUM} className="mb-2">
                      {step.name}
                    </Text>
                    <Text
                      variant={TextVariant.CAPTION}
                      className="text-text-secondary"
                    >
                      {step.description}
                    </Text>
                  </div>
                </div>
              </Card>
            ))}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </Card>
  );
};

export default HowToUse;
