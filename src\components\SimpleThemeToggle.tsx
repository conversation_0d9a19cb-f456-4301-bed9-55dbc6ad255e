"use client";

import { useTheme } from "@/hooks/useTheme";

export default function SimpleThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  const getIcon = () => {
    return theme === "light" ? "🌞" : "🌙";
  };

  const getLabel = () => {
    switch (theme) {
      case "light":
        return "浅色";
      case "dark":
        return "深色";
      default:
        return "主题";
    }
  };

  return (
    <button
      onClick={toggleTheme}
      className="flex items-center gap-2 px-4 py-2 rounded-lg bg-surface hover:bg-surface-light border border-border transition-all duration-200 hover-lift"
      title="切换主题"
    >
      <span className="text-lg">{getIcon()}</span>
      <span className="text-sm text-text-secondary">{getLabel()}</span>
    </button>
  );
}
