"use client";

import { ArrowUpTrayIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { Card } from "@/components/business/card";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { DocumentPreview } from "@/components/business/upload/components/DocumentPreview";
import { FileData } from "@/components/business/upload/types";
import { Button } from "@/components/ui/button";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";
import useSourceStore from "@/store/source";
import { useAppStore } from "@/store/useAppStore";
import KnowledgeBaseSkeleton from "./KnowledgeBaseSkeleton";
import ModalUpload from "./ModalUpload";

const KnowledgeBase = () => {
  const { setHeaderSlot } = useAppStore();
  const { currentRole } = useRoleStore();
  const [modalOpen, setModalOpen] = useState(false);
  const { t } = useLanguage();
  const {
    uploadFile,
    getFileList,
    fileList,
    deleteFile,
    isLoading,
    deletingFileId,
  } = useSourceStore();
  const handleComplete = async (files: FileData[]) => {
    const fileList = files.map((file) => ({
      fileName: file.name as string,
      s3Key: file.fileKey as string,
      size: file.size,
      type: file.type,
    }));
    await uploadFile(currentRole.personaId, fileList);
    // 移除关闭弹窗的逻辑，现在由 ModalUpload 组件处理
  };
  useEffect(() => {
    setHeaderSlot(
      <Button className="focus:outline-none" onClick={() => setModalOpen(true)}>
        <ArrowUpTrayIcon className="size-4"></ArrowUpTrayIcon>
        {t("button:uploadFile")}
      </Button>
    );
    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot, t]);
  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getFileList(currentRole.personaId);
    }
  }, [currentRole, getFileList]);

  // 显示骨架屏的条件：正在加载且没有数据
  if (isLoading && fileList.length === 0) {
    return <KnowledgeBaseSkeleton documentCount={3} />;
  }

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("information:knowledgeBase.file")}
        <span className="text-[13px] text-text-tertiary">
          {t("information:knowledgeBase.tip")}
        </span>
      </Text>

      <div className="grid gap-4">
        {fileList.length === 0 && (
          <EmptyState
            className="h-[calc(100vh-200px)]"
            type={EmptyType.NO_FILES}
            actions={[
              {
                label: t("button:uploadFile"),
                icon: <ArrowUpTrayIcon className="size-4"></ArrowUpTrayIcon>,
                onClick: () => {
                  setModalOpen(true);
                },
                variant: "default",
              },
            ]}
          ></EmptyState>
        )}
        <div className="flex gap-4 flex-col">
          {fileList.map((file) => (
            <DocumentPreview
              onRemove={(id) => deleteFile(id, currentRole.personaId)}
              key={file.id}
              isShowBar={false}
              isDeleting={deletingFileId === file.id}
              className="hover:border-ring"
              file={
                {
                  id: file.id,
                  name: file.fileName,
                  size: file.fileSize,
                  type: file.fileType,
                  category: "document",
                  status:
                    file.embeddingStatus === "SUCCESS" &&
                    file.imgStatus === "SUCCESS"
                      ? "success"
                      : file.embeddingStatus === "FAILED" ||
                        file.imgStatus === "FAILED"
                      ? "error"
                      : "embedding",
                  uploadTime: new Date(file.createdAt).toLocaleString(),
                  errors: [],
                  file: new File([], file.fileName, { type: file.fileType }),
                } as FileData
              }
            ></DocumentPreview>
          ))}
        </div>
      </div>
      <ModalUpload
        open={modalOpen}
        close={() => setModalOpen(false)}
        handleComplete={handleComplete}
      />
    </Card>
  );
};

export default KnowledgeBase;
