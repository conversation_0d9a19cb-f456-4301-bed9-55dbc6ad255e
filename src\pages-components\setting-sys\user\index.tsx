"use client";

import { Card } from "@/components/business/card";
import RoleForm from "@/components/business/modal/RoleForm";
import { IPersona } from "@/services/api/persona";
import useRoleStore from "@/store/persona";

const UserSettings = () => {
  const { updateRole, currentRole } = useRoleStore();
  const handleCreate = async (data: IPersona) => {
    const postData = {
      ...data,
      personaId: currentRole.personaId,
    };
    await updateRole(postData);
  };

  return (
    <Card>
      <RoleForm
        key={currentRole?.personaId || "empty"} // 使用 key 强制重新渲染
        defaultValues={currentRole}
        isUpdate
        onConfirm={(data) => handleCreate(data)}
      />
    </Card>
  );
};

export default UserSettings;
