import { Card } from "@/components/business/card";
import { Skeleton } from "@/components/ui/skeleton";

const RecentContentSkeleton = () => (
  <Card>
    {/* 标题区域骨架屏 */}
    <div className="mb-4 flex justify-between items-center">
      {/* 主标题骨架屏 */}
      <Skeleton className="h-6 w-24" />
      {/* "查看全部" 按钮骨架屏 */}
      <Skeleton className="h-8 w-16 rounded-md" />
    </div>

    {/* 内容列表骨架屏 */}
    <div className="grid gap-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index} className="bg-surface-light">
          <div className="flex gap-4 items-center">
            {/* 左侧内容信息骨架屏 */}
            <div className="flex-1">
              {/* 内容标题骨架屏 */}
              <Skeleton className="h-4 w-32 mb-2" />
              {/* 生成时间骨架屏 */}
              <Skeleton className="h-3 w-24" />
            </div>

            {/* 右侧状态骨架屏 */}
            <div className="flex justify-end">
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </Card>
      ))}
    </div>
  </Card>
);

export default RecentContentSkeleton;
