import { useLanguage } from "@/hooks/useLanguage";
import type { StatusBadgeProps } from "../types";

export const StatusBadge = ({ status }: StatusBadgeProps) => {
  const { t } = useLanguage();

  const configs: Record<
    string,
    { bg: string; text: string; labelKey: string }
  > = {
    pending: {
      bg: "bg-info-alpha-10",
      text: "text-info",
      labelKey: "upload:status.pending",
    },
    uploading: {
      bg: "bg-warning-alpha-10",
      text: "text-warning",
      labelKey: "upload:status.uploading",
    },
    success: {
      bg: "bg-success-alpha-10",
      text: "text-success",
      labelKey: "upload:status.success",
    },
    error: {
      bg: "bg-error-alpha-10",
      text: "text-error",
      labelKey: "upload:status.error",
    },
    embedding: {
      bg: "bg-warning-alpha-10",
      text: "text-warning",
      labelKey: "upload:status.embedding",
    },
  };

  const config = configs[status] || configs.pending;

  return (
    <span className={`px-2 py-1 rounded text-xs ${config.bg} ${config.text}`}>
      {t(config.labelKey)}
    </span>
  );
};
