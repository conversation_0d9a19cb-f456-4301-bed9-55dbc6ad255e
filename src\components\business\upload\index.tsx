import { FileText, Image as ImageIcon } from "lucide-react";
import React, { useRef, useCallback, useEffect } from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { DocumentPreview } from "./components/DocumentPreview";
import { DropzoneArea } from "./components/DropzoneArea";
import { ImagePreview } from "./components/ImagePreview";
import { useDragAndDrop } from "./hooks/useDragAndDrop";
import { useFileUpload } from "./hooks/useFileUpload";
import type { UniversalUploadSystemProps } from "./types";
import { mergeUploadConfig } from "./utils/uploadConfig";

// 主组件
const UniversalUploadSystem = ({
  config: userConfig,
  onUploadComplete,
  onFileRemove,
  className,
}: UniversalUploadSystemProps = {}) => {
  const { t } = useLanguage();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const previousCompletedCountRef = useRef(0);

  // 合并配置
  const config = mergeUploadConfig(userConfig);

  const {
    files,
    uploading,
    groupedFiles,
    processFiles,
    handleUpload,
    removeFile,
    clearAll,
  } = useFileUpload(config);

  const {
    isDragActive,
    isDragAccept,
    isDragReject,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
  } = useDragAndDrop(uploading, processFiles);

  // 监听文件状态变化，触发上传完成回调
  useEffect(() => {
    if (onUploadComplete) {
      const completedFiles = files.filter((file) => file.status === "success");
      if (completedFiles.length > 0) {
        // 检查是否有新完成的文件
        if (completedFiles.length > previousCompletedCountRef.current) {
          // 开发环境下显示上传完成信息
          if (process.env.NODE_ENV === "development") {
            console.warn(
              "上传完成回调触发:",
              completedFiles.map((file) => ({
                id: file.id,
                name: file.name,
                fileKey: file.fileKey,
                size: file.size,
                status: file.status,
              }))
            );
          }

          // 传递包含总数信息的对象
          onUploadComplete({
            files: completedFiles,
            totalCount: files.length,
            completedCount: completedFiles.length,
          });

          previousCompletedCountRef.current = completedFiles.length;
        }
      }
    }
  }, [files, onUploadComplete]);

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        processFiles(selectedFiles);
      }
      e.target.value = "";
    },
    [processFiles]
  );

  const handleClick = useCallback(() => {
    if (!uploading && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [uploading]);

  // 增强的文件删除处理，包含回调
  const handleFileRemove = useCallback(
    (fileId: string) => {
      const fileToRemove = files.find((f) => f.id === fileId);
      if (fileToRemove && onFileRemove) {
        onFileRemove(fileToRemove);
      }
      removeFile(fileId);
    },
    [files, onFileRemove, removeFile]
  );

  return (
    <div className={`text-text-primary ${className || ""}`}>
      <div className="p-6">
        {/* 拖拽上传区域 */}
        <DropzoneArea
          isDragActive={isDragActive}
          isDragAccept={isDragAccept}
          isDragReject={isDragReject}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleClick}
          fileInputRef={fileInputRef}
          onFileSelect={handleFileSelect}
          uploading={uploading}
          config={config}
          currentFileCount={files.length}
        />

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="mt-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold">
                {t("upload:fileList.title")} ({files.length}/
                {config.maxFiles || 5})
              </h2>
              <div className="space-x-3">
                <button
                  onClick={handleUpload}
                  disabled={
                    uploading ||
                    !files.some(
                      (f) => f.status === "pending" && f.errors.length === 0
                    )
                  }
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {uploading
                    ? t("upload:actions.uploading")
                    : t("upload:actions.upload")}
                </button>
                <button
                  onClick={clearAll}
                  className="px-4 py-2 bg-surface-lighter text-text-primary rounded-lg hover:bg-border transition-colors"
                >
                  {t("upload:actions.clearAll")}
                </button>
              </div>
            </div>

            {/* 分类显示文件 */}
            <div className="space-y-6">
              {groupedFiles.image &&
                (config.mode === "image" || config.mode === "mixed") && (
                  <div>
                    <h3 className="text-md font-medium text-primary mb-3 flex items-center">
                      <ImageIcon className="w-5 h-5 mr-2" />
                      {t("upload:fileList.categories.image")} (
                      {groupedFiles.image.length})
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      {groupedFiles.image.map((file) => (
                        <ImagePreview
                          key={file.id}
                          file={file}
                          onRemove={handleFileRemove}
                        />
                      ))}
                    </div>
                  </div>
                )}

              {groupedFiles.document &&
                (config.mode === "document" || config.mode === "mixed") && (
                  <div>
                    <h3 className="text-md font-medium text-accent mb-3 flex items-center">
                      <FileText className="w-5 h-5 mr-2" />
                      {t("upload:fileList.categories.document")} (
                      {groupedFiles.document.length})
                    </h3>
                    <div className="space-y-3">
                      {groupedFiles.document.map((file) => (
                        <DocumentPreview
                          key={file.id}
                          file={file}
                          onRemove={handleFileRemove}
                        />
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UniversalUploadSystem;
