import { useCallback, useMemo } from "react";
import { useLanguage } from "@/hooks/useLanguage";
import {
  DEFAULT_PERSONA,
  StepIndex,
} from "@/pages-components/work-space/generating/constants";
import { Processing } from "@/pages-components/work-space/generating/index";
import { ProcessingStage } from "@/store/generate";

/**
 * 步骤数据接口
 */
export interface StepData {
  title: string;
  stage: Processing;
  details?: string[] | string;
  isOutput?: boolean;
}

/**
 * Hook参数接口
 */
interface UseGeneratingStepsParams {
  currentSteps: ProcessingStage;
  currentRole?: {
    name?: string;
    personality?: string;
    topics?: string;
  };
  fileList: Array<{ fileName: string }>;
  thinkingData: string;
}

/**
 * 生成步骤管理的自定义Hook
 *
 * 负责：
 * - 计算每个步骤的状态
 * - 生成步骤配置数据
 * - 处理默认值和国际化
 */
export const useGeneratingSteps = ({
  currentSteps,
  currentRole,
  fileList,
  thinkingData,
}: UseGeneratingStepsParams) => {
  const { t } = useLanguage();

  // 步骤状态计算函数
  const getStepStage = useCallback(
    (stepIndex: StepIndex): Processing => {
      switch (stepIndex) {
        case StepIndex.USER_ANALYSIS:
          return currentSteps === ProcessingStage.INIT ||
            currentSteps === ProcessingStage.KEYWORD_GENERATION
            ? Processing.PROCESSING
            : Processing.COMPLETED;

        case StepIndex.KNOWLEDGE_SEARCH:
          if (
            currentSteps === ProcessingStage.INIT ||
            currentSteps === ProcessingStage.KEYWORD_GENERATION
          ) {
            return Processing.INIT;
          }
          return currentSteps === ProcessingStage.VECTOR_SEARCH
            ? Processing.PROCESSING
            : Processing.COMPLETED;

        case StepIndex.CONTENT_GENERATION:
          if (
            currentSteps === ProcessingStage.INIT ||
            currentSteps === ProcessingStage.KEYWORD_GENERATION ||
            currentSteps === ProcessingStage.VECTOR_SEARCH
          ) {
            return Processing.INIT;
          }
          return currentSteps === ProcessingStage.CONTENT_GENERATION
            ? Processing.PROCESSING
            : Processing.COMPLETED;

        default:
          return Processing.COMPLETED;
      }
    },
    [currentSteps]
  );

  // 生成步骤配置
  const steps = useMemo((): StepData[] => {
    return [
      {
        title: t("workspace:generating.steps.userAnalysis.title"),
        stage: getStepStage(StepIndex.USER_ANALYSIS),
        details: [
          t("workspace:generating.steps.userAnalysis.details.currentPersona", {
            personaName: currentRole?.name || DEFAULT_PERSONA.name,
          }),
          t(
            "workspace:generating.steps.userAnalysis.details.personaDescription",
            {
              personaName: currentRole?.name || DEFAULT_PERSONA.name,
              personalityTraits:
                currentRole?.personality || DEFAULT_PERSONA.personality,
              expertTopics: currentRole?.topics || DEFAULT_PERSONA.topics,
            }
          ),
        ],
      },
      {
        title: t("workspace:generating.steps.knowledgeSearch.title"),
        stage: getStepStage(StepIndex.KNOWLEDGE_SEARCH),
        details: [
          t(
            "workspace:generating.steps.knowledgeSearch.details.searchingDatabase"
          ),
          t(
            "workspace:generating.steps.knowledgeSearch.details.analyzingFiles"
          ),
          ...fileList.map((file) => file.fileName),
        ],
      },
      {
        title: t("workspace:generating.steps.contentGeneration.title"),
        stage: getStepStage(StepIndex.CONTENT_GENERATION),
        isOutput: true,
        details: thinkingData,
      },
    ];
  }, [t, getStepStage, currentRole, fileList, thinkingData]);

  return {
    steps,
    getStepStage,
  };
};
