import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  uploadFileApi,
  IFile,
  fileListApi,
  deleteFileApi,
  createSourceApi,
  sourceListApi,
  deleteSourceApi,
  ISource,
} from "@/services/api/source";
interface PhotoStore {
  fileList: IFile[];
  isLoading: boolean;
  isCreating: boolean;
  deletingSourceId: string | null;
  deletingFileId: string | null;
  uploadFile: (
    personaId: string,
    files: { fileName: string; s3Key: string; size: number; type: string }[]
  ) => Promise<void>;
  getFileList: (personaId: string) => Promise<void>;
  deleteFile: (fileId: string, personaId: string) => Promise<void>;
  createSource: (data: ISource) => Promise<void>;
  sourceList: ISource[];
  getSourceList: (personaId: string) => Promise<void>;
  sourceCount: number;
  deleteSource: (sourceId: string, personaId: string) => Promise<void>;
}
const useSourceStore = create<PhotoStore>()(
  immer(
    (set, get): PhotoStore => ({
      fileList: [],
      isLoading: false,
      isCreating: false,
      deletingSourceId: null,
      deletingFileId: null,
      sourceCount: 0,
      sourceList: [],
      uploadFile: async (
        personaId: string,
        files: { fileName: string; s3Key: string; size: number; type: string }[]
      ) => {
        await uploadFileApi({
          personaId,
          files,
        });
        await get().getFileList(personaId);
      },
      getFileList: async (personaId: string) => {
        try {
          set({ isLoading: true });
          const res = await fileListApi({
            personaId,
          });
          if (res.success) {
            set({
              fileList: res.data?.files?.files || [],
            });
          }
        } finally {
          set({ isLoading: false });
        }
      },
      deleteFile: async (fileId: string, personaId: string) => {
        try {
          set({ deletingFileId: fileId });
          await deleteFileApi({ fileId });
          await get().getFileList(personaId);
        } finally {
          set({ deletingFileId: null });
        }
      },
      createSource: async (data: ISource) => {
        try {
          set({ isCreating: true });
          await createSourceApi(data);
          await get().getSourceList(data.personaId);
        } finally {
          set({ isCreating: false });
        }
      },
      getSourceList: async (personaId: string) => {
        try {
          set({ isLoading: true });
          const res = await sourceListApi(personaId);
          if (res.success) {
            set({
              sourceList: res.data?.sources || [],
              sourceCount: res.data?.count || 0,
            });
          }
        } finally {
          set({ isLoading: false });
        }
      },
      deleteSource: async (sourceId: string, personaId: string) => {
        try {
          set({ deletingSourceId: sourceId });
          await deleteSourceApi({ sourceId });
          await get().getSourceList(personaId);
        } finally {
          set({ deletingSourceId: null });
        }
      },
    })
  )
);

export default useSourceStore;
