export type UploadMode = "image" | "document" | "mixed";

export interface UploadConfig {
  mode: UploadMode;
  accept?: {
    image?: string[];
    document?: string[];
  };
  maxSize?: {
    image?: number;
    document?: number;
  };
  multiple?: boolean;
  maxFiles?: number; // 最大文件数量限制
  showPreview?: boolean;
  showProgress?: boolean;
}

export interface FileData {
  id: string;
  name: string;
  size: number;
  type: string;
  category: string;
  status: string;
  uploadTime: string;
  errors: string[];
  preview?: string;
  file: File;
  progress?: number;
  uploadSpeed?: string;
  remainingTime?: string;
  fileKey?: string;
  controller?: AbortController;
}

export interface DropzoneAreaProps {
  isDragActive: boolean;
  isDragAccept: boolean;
  isDragReject: boolean;
  onDragEnter: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  onClick: () => void;
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  onFileSelect: (e: React.ChangeEvent<HTMLInputElement>) => void;
  uploading: boolean;
  config: UploadConfig;
  currentFileCount?: number; // 当前已选择的文件数量
}

export interface FileIconProps {
  file: File;
  size?: string;
}

export interface StatusIconProps {
  status: string;
}

export interface StatusBadgeProps {
  status: string;
}

export interface ProgressBarProps {
  fileId: string;
  file?: FileData;
}

export interface ErrorMessageProps {
  errors: string[];
}

export interface ImageModalProps {
  src: string;
  alt: string;
  onClose: () => void;
}

export interface ImageGalleryModalProps {
  images: Array<{
    src: string;
    alt: string;
    id: string;
  }>;
  currentIndex: number;
  onClose: () => void;
  onIndexChange?: (index: number) => void;
}

export interface FilePreviewProps {
  file: FileData;
  onRemove: (id: string) => void;
  isShowBar?: boolean;
  className?: string;
  isDeleting?: boolean;
}

export interface UploadCompleteInfo {
  files: FileData[];
  totalCount: number;
  completedCount: number;
}

export interface UniversalUploadSystemProps {
  config?: UploadConfig;
  onUploadComplete?: (info: UploadCompleteInfo) => void;
  onFileRemove?: (file: FileData) => void;
  className?: string;
}

// 分片上传相关类型
export interface ChunkUploadConfig {
  chunkSize?: number; // 分片大小，默认 5MB
  maxConcurrent?: number; // 最大并发数，默认 3
  maxRetries?: number; // 最大重试次数，默认 3
  enableMD5?: boolean; // 是否启用 MD5 校验
}

export interface UploadPart {
  PartNumber: number;
  ETag: string;
}

export interface FileItem {
  file: File;
  id: string;
  controller?: AbortController;
}

export interface UploadProgress {
  fileId: string;
  progress: number;
  uploadSpeed?: string;
  remainingTime?: string;
  fileKey?: string; // 服务器返回的文件键
  status: "pending" | "uploading" | "success" | "error" | "cancelled";
}
