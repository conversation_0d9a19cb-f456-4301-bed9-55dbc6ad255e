import { Loader } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Card } from "@/components/business/card";
import { Select } from "@/components/business/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RequiredLabel } from "@/components/ui/RequiredLabel";
import { useLanguage } from "@/hooks/useLanguage";
import { ISource } from "@/services/api/source";

export interface SourceFormData {
  sourceName: string;
  sourceType: ISource["type"];
  urlAddress: string;
  contentTypes: string[];
}

interface SourceFormProps {
  onSubmit?: (data: SourceFormData) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  defaultValues?: Partial<SourceFormData>;
}

const SourceForm = ({
  onSubmit,
  onCancel,
  defaultValues,
  isLoading = false,
}: SourceFormProps) => {
  const { t } = useLanguage();
  const [isFetching, setIsFetching] = useState(false);

  // 初始化表单
  const form = useForm<SourceFormData>({
    defaultValues: {
      sourceName: "",
      sourceType: "url",
      urlAddress: "",
      contentTypes: [],
      ...defaultValues,
    },
    mode: "onChange", // 实时验证
  });

  // 表单提交处理
  const handleFormSubmit = async (data: SourceFormData) => {
    setIsFetching(true);
    try {
      await onSubmit?.(data);
      setIsFetching(false);
    } catch (error) {
      console.error("提交表单失败:", error);
      setIsFetching(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    onCancel?.();
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className="text-text-secondary flex flex-col gap-6"
      >
        {/* 信息源名称 - 必填 */}
        <FormField
          control={form.control}
          name="sourceName"
          rules={{
            required: t("modal:addSource.validation.sourceNameRequired"),
            minLength: {
              value: 2,
              message: t("modal:addSource.validation.sourceNameMinLength"),
            },
          }}
          render={({ field }) => (
            <FormItem>
              <RequiredLabel required>
                {t("modal:addSource.sourceName")}
              </RequiredLabel>
              <FormControl>
                <Input
                  placeholder={t("modal:addSource.sourceNamePlaceholder")}
                  className="h-12"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 信息源类型 - 必填 */}
        <FormField
          control={form.control}
          name="sourceType"
          rules={{
            required: t("modal:addSource.validation.sourceTypeRequired"),
          }}
          render={({ field }) => (
            <FormItem>
              <RequiredLabel required>
                {t("modal:addSource.sourceType")}
              </RequiredLabel>
              <FormControl>
                <Select
                  placeholder={t("modal:addSource.sourceTypePlaceholder")}
                  className="w-full"
                  options={[
                    { name: t("modal:addSource.rssType"), value: "rss" },
                    {
                      name: t("modal:addSource.websiteType"),
                      value: "url",
                    },
                    { name: t("modal:addSource.apiType"), value: "account" },
                  ]}
                  value={field.value}
                  onValueChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* URL 地址 - 必填 */}
        <FormField
          control={form.control}
          name="urlAddress"
          rules={{
            required: t("modal:addSource.validation.urlRequired"),
            pattern: {
              value: /^https?:\/\/.+/,
              message: t("modal:addSource.validation.urlInvalid"),
            },
          }}
          render={({ field }) => (
            <FormItem>
              <RequiredLabel required>
                {t("modal:addSource.urlAddress")}
              </RequiredLabel>
              <FormControl>
                <Input
                  placeholder={t("modal:addSource.urlPlaceholder")}
                  className="h-12"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 内容类型 - 可选 */}
        <FormField
          control={form.control}
          name="contentTypes"
          render={({ field }) => (
            <FormItem>
              <RequiredLabel>{t("modal:addSource.contentType")}</RequiredLabel>
              <FormControl>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    {
                      id: "techNews",
                      value: "techNews",
                      label: t("modal:addSource.techNews"),
                    },
                    {
                      id: "lifestyle",
                      value: "lifestyle",
                      label: t("modal:addSource.lifestyle"),
                    },
                    {
                      id: "fashion",
                      value: "fashion",
                      label: t("modal:addSource.fashion"),
                    },
                    {
                      id: "food",
                      value: "food",
                      label: t("modal:addSource.food"),
                    },
                  ].map((contentType) => (
                    <Card
                      key={contentType.id}
                      className={`hover:border-ring transition-colors ${
                        field.value?.includes(contentType.value)
                          ? "border-primary bg-primary-alpha-10"
                          : "border-border"
                      }`}
                    >
                      <div
                        className="flex items-center gap-2 cursor-pointer"
                        onClick={() => {
                          // 触发复选框的点击事件
                          const checkbox = document.getElementById(
                            contentType.id
                          ) as HTMLInputElement;
                          if (checkbox) {
                            checkbox.click();
                          }
                        }}
                      >
                        <Checkbox
                          id={contentType.id}
                          className="bg-white-alpha-20"
                          checked={field.value?.includes(contentType.value)}
                          onCheckedChange={(checked) => {
                            const currentValues = field.value || [];
                            if (checked) {
                              // 添加到数组
                              field.onChange([
                                ...currentValues,
                                contentType.value,
                              ]);
                            } else {
                              // 从数组中移除
                              field.onChange(
                                currentValues.filter(
                                  (value) => value !== contentType.value
                                )
                              );
                            }
                          }}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Label
                          htmlFor={contentType.id}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {contentType.label}
                        </Label>
                      </div>
                    </Card>
                  ))}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 按钮组 */}
        <div className="grid gap-10 grid-cols-2">
          <Button type="button" variant="secondary" onClick={handleCancel}>
            {t("button:cancel")}
          </Button>
          <Button
            type="submit"
            disabled={isFetching || isLoading}
            className="text-text-primary"
          >
            {(isFetching || isLoading) && (
              <Loader className="mr-1 h-4 w-4 spin360" />
            )}
            {t("button:add")}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default SourceForm;
