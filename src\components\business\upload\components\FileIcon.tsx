import { File, Image as ImageIcon } from "lucide-react";
import type { FileIconProps } from "../types";
import { getFileCategory } from "../utils/fileUtils";

export const FileIcon = ({ file, size = "w-8 h-8" }: FileIconProps) => {
  const category = getFileCategory(file);

  if (category === "image") {
    return <ImageIcon className={`${size} text-primary`} />;
  }

  if (file.type === "application/pdf") {
    return (
      <div
        className={`${size} flex items-center justify-center text-error text-xl`}
      >
        📄
      </div>
    );
  }

  if (file.type.includes("word")) {
    return (
      <div
        className={`${size} flex items-center justify-center text-accent text-xl`}
      >
        📝
      </div>
    );
  }

  if (file.type === "application/json") {
    return (
      <div
        className={`${size} flex items-center justify-center text-success text-xl`}
      >
        📋
      </div>
    );
  }

  return <File className={`${size} text-text-secondary`} />;
};
