"use client";

import { ArrowUpTrayIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";

interface PhotoHeaderProps {
  onUpload: () => void;
}

const PhotoHeader = ({ onUpload }: PhotoHeaderProps) => {
  const { t } = useLanguage();

  return (
    <div className="flex gap-2 items-center">
      <Button variant="secondary" onClick={onUpload}>
        <ArrowUpTrayIcon className="size-4" />
        {t("camera:photoAnalyzer.uploadPhoto")}
      </Button>
    </div>
  );
};

export default PhotoHeader;
