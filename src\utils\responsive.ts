import React from "react";
import { BREAKPOINTS } from "@/constants";

/**
 * 响应式设计工具函数
 */

// 断点类型
export type Breakpoint = keyof typeof BREAKPOINTS;

// 响应式值类型
export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>;

/**
 * 检查当前屏幕是否匹配指定断点
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState(() => {
    if (typeof window === "undefined") {
      return false;
    }
    return window.matchMedia(query).matches;
  });

  React.useEffect(() => {
    if (typeof window === "undefined") {
      return;
    }

    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);

    mediaQuery.addEventListener("change", handler);
    return () => mediaQuery.removeEventListener("change", handler);
  }, [query]);

  return matches;
}

/**
 * 获取断点媒体查询字符串
 */
export function getBreakpointQuery(
  breakpoint: Breakpoint,
  type: "min" | "max" = "min"
): string {
  const size = BREAKPOINTS[breakpoint];
  return `(${type}-width: ${size})`;
}

/**
 * 检查是否为移动设备断点
 */
export function useIsMobile(): boolean {
  return useMediaQuery(getBreakpointQuery("md", "max"));
}

/**
 * 检查是否为平板设备断点
 */
export function useIsTablet(): boolean {
  const isMobile = useIsMobile();
  const isDesktop = useMediaQuery(getBreakpointQuery("lg"));
  return !isMobile && !isDesktop;
}

/**
 * 检查是否为桌面设备断点
 */
export function useIsDesktop(): boolean {
  return useMediaQuery(getBreakpointQuery("lg"));
}

/**
 * 获取当前断点
 */
export function useCurrentBreakpoint(): Breakpoint {
  const isXs = useMediaQuery(getBreakpointQuery("xs", "max"));
  const isSm = useMediaQuery(getBreakpointQuery("sm"));
  const isMd = useMediaQuery(getBreakpointQuery("md"));
  const isLg = useMediaQuery(getBreakpointQuery("lg"));
  const isXl = useMediaQuery(getBreakpointQuery("xl"));
  const is2xl = useMediaQuery(getBreakpointQuery("2xl"));

  if (is2xl) {
    return "2xl";
  }
  if (isXl) {
    return "xl";
  }
  if (isLg) {
    return "lg";
  }
  if (isMd) {
    return "md";
  }
  if (isSm) {
    return "sm";
  }
  if (isXs) {
    return "xs";
  }
  return "xs";
}

/**
 * 解析响应式值
 */
export function resolveResponsiveValue<T>(
  value: ResponsiveValue<T>,
  currentBreakpoint: Breakpoint
): T {
  if (typeof value !== "object" || value === null) {
    return value as T;
  }

  const responsiveValue = value as Partial<Record<Breakpoint, T>>;
  const breakpointOrder: Breakpoint[] = ["xs", "sm", "md", "lg", "xl", "2xl"];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);

  // 从当前断点向下查找最近的值
  for (let i = currentIndex; i >= 0; i--) {
    const breakpoint = breakpointOrder[i];
    if (
      breakpoint in responsiveValue &&
      responsiveValue[breakpoint] !== undefined
    ) {
      return responsiveValue[breakpoint] as T;
    }
  }

  // 如果没有找到，返回第一个可用的值
  for (const breakpoint of breakpointOrder) {
    if (
      breakpoint in responsiveValue &&
      responsiveValue[breakpoint] !== undefined
    ) {
      return responsiveValue[breakpoint] as T;
    }
  }

  // 如果都没有，返回默认值
  return value as T;
}

/**
 * 生成响应式类名
 */
export function generateResponsiveClasses<T extends string>(
  value: ResponsiveValue<T>,
  prefix: string = ""
): string {
  if (typeof value !== "object" || value === null) {
    return prefix ? `${prefix}${value}` : (value as string);
  }

  const classes: string[] = [];
  const breakpointOrder: Breakpoint[] = ["xs", "sm", "md", "lg", "xl", "2xl"];

  for (const breakpoint of breakpointOrder) {
    if (breakpoint in value && value[breakpoint] !== undefined) {
      const className = prefix
        ? `${prefix}${value[breakpoint]}`
        : (value[breakpoint] as string);
      if (breakpoint === "xs") {
        classes.push(className);
      } else {
        classes.push(`${breakpoint}:${className}`);
      }
    }
  }

  return classes.join(" ");
}

/**
 * 创建响应式样式对象
 */
export function createResponsiveStyles<T>(
  value: ResponsiveValue<T>,
  property: string
): Record<string, T> {
  if (typeof value !== "object" || value === null) {
    return { [property]: value };
  }

  const responsiveValue = value as Partial<Record<Breakpoint, T>>;
  const styles: Record<string, T> = {};
  const breakpointOrder: Breakpoint[] = ["xs", "sm", "md", "lg", "xl", "2xl"];

  for (const breakpoint of breakpointOrder) {
    if (
      breakpoint in responsiveValue &&
      responsiveValue[breakpoint] !== undefined
    ) {
      if (breakpoint === "xs") {
        styles[property] = responsiveValue[breakpoint] as T;
      } else {
        styles[`@media (min-width: ${BREAKPOINTS[breakpoint]})`] = {
          [property]: responsiveValue[breakpoint],
        } as T;
      }
    }
  }

  return styles;
}

/**
 * 容器查询工具（实验性）
 */
export function useContainerQuery(
  containerRef: React.RefObject<HTMLElement>,
  query: string
): boolean {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    if (!containerRef.current || typeof window === "undefined") {
      return;
    }

    // 这里可以实现容器查询逻辑
    // 目前浏览器支持有限，可以使用 ResizeObserver 作为替代
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        // 简单的容器查询实现 - 避免使用 eval
        try {
          // 解析简单的查询，如 "width > 300"
          const widthMatch = query.match(/width\s*([><=]+)\s*(\d+)/);
          const heightMatch = query.match(/height\s*([><=]+)\s*(\d+)/);

          let result = true;

          if (widthMatch) {
            const [, operator, value] = widthMatch;
            const targetWidth = parseInt(value, 10);
            switch (operator) {
              case ">":
                result = result && width > targetWidth;
                break;
              case "<":
                result = result && width < targetWidth;
                break;
              case ">=":
                result = result && width >= targetWidth;
                break;
              case "<=":
                result = result && width <= targetWidth;
                break;
              case "=":
              case "==":
                result = result && width === targetWidth;
                break;
            }
          }

          if (heightMatch) {
            const [, operator, value] = heightMatch;
            const targetHeight = parseInt(value, 10);
            switch (operator) {
              case ">":
                result = result && height > targetHeight;
                break;
              case "<":
                result = result && height < targetHeight;
                break;
              case ">=":
                result = result && height >= targetHeight;
                break;
              case "<=":
                result = result && height <= targetHeight;
                break;
              case "=":
              case "==":
                result = result && height === targetHeight;
                break;
            }
          }

          setMatches(result);
        } catch (error) {
          console.warn("Container query parsing error:", error);
          setMatches(false);
        }
      }
    });

    observer.observe(containerRef.current);

    return () => {
      observer.disconnect();
    };
  }, [containerRef, query]);

  return matches;
}

/**
 * 响应式字体大小工具
 */
export function getResponsiveFontSize(
  baseSize: number,
  scale: number = 1.2
): ResponsiveValue<string> {
  return {
    xs: `${baseSize * 0.8}rem`,
    sm: `${baseSize * 0.9}rem`,
    md: `${baseSize}rem`,
    lg: `${baseSize * scale}rem`,
    xl: `${baseSize * scale * 1.1}rem`,
    "2xl": `${baseSize * scale * 1.2}rem`,
  };
}

/**
 * 响应式间距工具
 */
export function getResponsiveSpacing(
  baseSpacing: number
): ResponsiveValue<string> {
  return {
    xs: `${baseSpacing * 0.5}rem`,
    sm: `${baseSpacing * 0.75}rem`,
    md: `${baseSpacing}rem`,
    lg: `${baseSpacing * 1.25}rem`,
    xl: `${baseSpacing * 1.5}rem`,
    "2xl": `${baseSpacing * 2}rem`,
  };
}
