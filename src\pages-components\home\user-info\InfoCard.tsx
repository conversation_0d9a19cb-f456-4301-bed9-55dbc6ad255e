"use client";

import { Card } from "@/components/business/card";
import { Text, TextVariant } from "@/components/ui/Text";
import { cn } from "@/lib/utils";

interface InfoCardProps {
  title: string;
  content: React.ReactNode;
  className?: string;
}

const InfoCard = ({ title, content, className }: InfoCardProps) => (
  <Card className={cn("bg-surface hover:border-ring", className)}>
    <Text
      variant={TextVariant.BODY_MEDIUM}
      className="border-b border-border pb-2 relative pl-4"
    >
      {title}
      <span className="absolute bottom-4 left-0 bg-ring h-2 w-2 rounded-full"></span>
    </Text>
    <div className="mt-2">{content}</div>
  </Card>
);

export default InfoCard;
