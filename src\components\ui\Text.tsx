import React, { JSX } from "react";
import { TextVariant } from "@/types";
import type { TextProps } from "@/types";
import { cn } from "@/utils";

// 文本变体样式映射
const textVariantStyles: Record<TextVariant, string> = {
  [TextVariant.H1]: "text-4xl font-bold leading-tight",
  [TextVariant.H2]: "text-3xl font-semibold leading-tight",
  [TextVariant.H3]: "text-2xl font-semibold leading-snug",
  [TextVariant.H4]: "text-xl font-medium leading-snug",
  [TextVariant.H5]: "text-lg font-medium leading-normal",
  [TextVariant.H6]: "text-base font-medium leading-normal",
  [TextVariant.TITLE_LARGE]: "text-2xl font-semibold leading-snug",
  [TextVariant.TITLE_MEDIUM]: "text-xl font-medium leading-snug",
  [TextVariant.TITLE_SMALL]: "text-lg font-medium leading-normal",
  [TextVariant.BODY_LARGE]: "text-lg leading-relaxed",
  [TextVariant.BODY_MEDIUM]: "text-base leading-normal",
  [TextVariant.BODY_SMALL]: "text-sm leading-normal",
  [TextVariant.CAPTION]: "text-xs leading-tight",
};

// 默认 HTML 标签映射
const defaultTagMapping: Record<TextVariant, keyof JSX.IntrinsicElements> = {
  [TextVariant.H1]: "h1",
  [TextVariant.H2]: "h2",
  [TextVariant.H3]: "h3",
  [TextVariant.H4]: "h4",
  [TextVariant.H5]: "h5",
  [TextVariant.H6]: "h6",
  [TextVariant.TITLE_LARGE]: "div",
  [TextVariant.TITLE_MEDIUM]: "div",
  [TextVariant.TITLE_SMALL]: "div",
  [TextVariant.BODY_LARGE]: "p",
  [TextVariant.BODY_MEDIUM]: "p",
  [TextVariant.BODY_SMALL]: "p",
  [TextVariant.CAPTION]: "span",
};

/**
 * 统一的文本组件
 *
 * @example
 * ```tsx
 * <Text variant={TextVariant.H1}>标题</Text>
 * <Text variant={TextVariant.BODY_MEDIUM} as="span">正文</Text>
 * <Text variant={TextVariant.CAPTION} className="text-text-secondary">说明文字</Text>
 * ```
 */
export { TextVariant };
export const Text: React.FC<TextProps> = ({
  variant = TextVariant.BODY_MEDIUM,
  as,
  className,
  children,
  onClick,
  dangerouslySetInnerHTML,
  ...rest
}) => {
  // 确定要使用的 HTML 标签
  const tagName = (as || defaultTagMapping[variant]) as React.ElementType;

  // 合并样式类
  const classes = cn(
    textVariantStyles[variant],
    onClick && "cursor-pointer", // 如果有点击事件，添加指针样式
    className
  );

  // 构建 props 对象
  const elementProps = {
    className: classes,
    onClick,
    ...rest,
  };

  // 如果使用 dangerouslySetInnerHTML，不能同时传递 children
  if (dangerouslySetInnerHTML) {
    return React.createElement(tagName, {
      ...elementProps,
      dangerouslySetInnerHTML,
    });
  }

  return React.createElement(tagName, elementProps, children);
};

// 便捷的预设组件
export const Heading1: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.H1} {...props} />
);

export const Heading2: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.H2} {...props} />
);

export const Heading3: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.H3} {...props} />
);

export const Heading4: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.H4} {...props} />
);

export const Heading5: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.H5} {...props} />
);

export const Heading6: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.H6} {...props} />
);

export const Title: React.FC<
  Omit<TextProps, "variant"> & { size?: "large" | "medium" | "small" }
> = ({ size = "medium", ...props }) => {
  const variantMap = {
    large: TextVariant.TITLE_LARGE,
    medium: TextVariant.TITLE_MEDIUM,
    small: TextVariant.TITLE_SMALL,
  };

  return <Text variant={variantMap[size]} {...props} />;
};

export const Body: React.FC<
  Omit<TextProps, "variant"> & { size?: "large" | "medium" | "small" }
> = ({ size = "medium", ...props }) => {
  const variantMap = {
    large: TextVariant.BODY_LARGE,
    medium: TextVariant.BODY_MEDIUM,
    small: TextVariant.BODY_SMALL,
  };

  return <Text variant={variantMap[size]} {...props} />;
};

export const Caption: React.FC<Omit<TextProps, "variant">> = (props) => (
  <Text variant={TextVariant.CAPTION} {...props} />
);

// 特殊样式的文本组件
export const GradientText: React.FC<TextProps> = ({ className, ...props }) => (
  <Text className={cn("gradient-text", className)} {...props} />
);

export const ErrorText: React.FC<TextProps> = ({ className, ...props }) => (
  <Text className={cn("text-error", className)} {...props} />
);

export const SuccessText: React.FC<TextProps> = ({ className, ...props }) => (
  <Text className={cn("text-success", className)} {...props} />
);

export const WarningText: React.FC<TextProps> = ({ className, ...props }) => (
  <Text className={cn("text-warning", className)} {...props} />
);

export const SecondaryText: React.FC<TextProps> = ({ className, ...props }) => (
  <Text className={cn("text-text-secondary", className)} {...props} />
);

export const TertiaryText: React.FC<TextProps> = ({ className, ...props }) => (
  <Text className={cn("text-text-tertiary", className)} {...props} />
);

// 导出所有组件
export default Text;
