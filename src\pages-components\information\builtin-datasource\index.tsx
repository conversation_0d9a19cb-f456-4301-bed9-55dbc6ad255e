"use client";

import { useState, useMemo } from "react";
import { toast } from "sonner";
import { Card } from "@/components/business/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";

interface PlatformItem {
  name: string;
  desc: string;
  isConnection: boolean;
}

interface PlatformGroup {
  name: string;
  items: PlatformItem[];
}

const BuiltinDatasource = () => {
  const { t, currentLanguage } = useLanguage();
  const { roleList } = useRoleStore();
  // 初始化平台数据
  const initialPlatforms = useMemo(() => {
    const en = [
      {
        name: t("information:builtinDatasource.platforms.international"),
        items: [
          {
            name: "Reddit",
            desc: t("information:builtinDatasource.platforms.reddit"),
            isConnection: false,
          },
          {
            name: "X (Twitter)",
            desc: t("information:builtinDatasource.platforms.x"),
            isConnection: false,
          },
          {
            name: "Instagram",
            desc: t("information:builtinDatasource.platforms.instagram"),
            isConnection: false,
          },
        ],
      },
    ];
    const zh = [
      {
        name: t("information:builtinDatasource.platforms.chinese"),
        items: [
          { name: "小红书", desc: "生活方式分享", isConnection: false },
          { name: "B站", desc: "二次元内容创作", isConnection: false },
          { name: "知乎", desc: "专业问题解答", isConnection: false },
        ],
      },
    ];
    return currentLanguage === "en-US" ? en : zh;
  }, [t, currentLanguage]);

  // 使用状态管理平台连接状态
  const [platforms, setPlatforms] = useState<PlatformGroup[]>(initialPlatforms);

  // 处理连接状态切换
  const handleToggleConnection = (groupIndex: number, itemIndex: number) => {
    if (roleList.length === 0) {
      toast.error(t("components:empty.description.noRoles"));
      return;
    }
    setPlatforms((prevPlatforms) =>
      prevPlatforms.map((group, gIndex) =>
        gIndex === groupIndex
          ? {
              ...group,
              items: group.items.map((item, iIndex) =>
                iIndex === itemIndex
                  ? { ...item, isConnection: !item.isConnection }
                  : item
              ),
            }
          : group
      )
    );
  };
  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("information:builtinDatasource.title")}
        <span className="text-[13px] text-text-tertiary">
          {t("information:builtinDatasource.tip")}
        </span>
      </Text>
      <div className="grid gap-4 grid-cols-1 max-md:grid-cols-1">
        {platforms.map((group, groupIndex) => {
          return (
            <div
              key={group.name}
              className="bg-surface-light px-4 rounded-md py-2"
            >
              <Text variant={TextVariant.BODY_MEDIUM} className="py-2 mb-2">
                {group.name}
              </Text>
              <div className="grid gap-4">
                {group.items.map((item, itemIndex) => {
                  return (
                    <Card key={item.name}>
                      <div
                        className="flex items-center justify-between cursor-pointer select-none"
                        onClick={() => {
                          handleToggleConnection(groupIndex, itemIndex);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div>
                            <Checkbox
                              checked={item.isConnection}
                              className="bg-white-alpha-20"
                            ></Checkbox>
                          </div>
                          <div>
                            <Text variant={TextVariant.BODY_SMALL}>
                              {item.name}
                            </Text>
                            <Text
                              variant={TextVariant.CAPTION}
                              className="text-text-tertiary"
                            >
                              {item.desc}
                            </Text>
                          </div>
                        </div>
                        <Badge
                          variant={item.isConnection ? "default" : "secondary"}
                        >
                          {item.isConnection
                            ? t(
                                "information:builtinDatasource.platforms.connected"
                              )
                            : t(
                                "information:builtinDatasource.platforms.connect"
                              )}
                        </Badge>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

export default BuiltinDatasource;
