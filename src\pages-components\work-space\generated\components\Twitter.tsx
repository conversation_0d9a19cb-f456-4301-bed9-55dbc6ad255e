"use client";

import {
  ChatBubbleOvalLeftIcon,
  ArrowPathRoundedSquareIcon,
  HeartIcon,
  ShareIcon,
  EllipsisHorizontalIcon,
} from "@heroicons/react/24/outline";
import { useMemo } from "react";
import { ProfileIcon } from "@/components/ProfileIcon";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImageCarousel } from "@/components/ui/ImageCarousel";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useRoleStore from "@/store/persona";

interface TwitterProps {
  className?: string;
  data: IGenerateList;
  onPublish?: (data: IGenerateList) => void;
  onSave?: (data: IGenerateList) => void;
}

const Twitter = ({ className, data, onPublish, onSave }: TwitterProps) => {
  const { t } = useLanguage();
  const { currentRole } = useRoleStore();

  // 图片相关的计算属性
  const photos = useMemo(() => data.photos || [], [data.photos]);

  return (
    <div>
      <Card
        className={`w-full mx-auto  border-border hover:border-ring ${className}`}
      >
        {/* 用户信息头部 */}
        <div className="flex items-start gap-3 p-4">
          <Avatar className="w-12 h-12 flex-shrink-0">
            <AvatarImage src="" alt={currentRole.name} />
            <AvatarFallback className="bg-blue-500 text-white font-semibold">
              <ProfileIcon seed={currentRole.personaId} size={60}></ProfileIcon>
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-bold text-white">{currentRole.name}</span>
                <span className="text-gray-400 text-sm">
                  @
                  {currentRole.name?.toLowerCase().replace(/\s+/g, "") ||
                    "user"}
                </span>
              </div>
              <EllipsisHorizontalIcon className="w-5 h-5 text-gray-400 hover:text-white cursor-pointer" />
            </div>

            {/* 推文内容 */}
            <div className="mt-2">
              <p className="text-white text-sm leading-relaxed mb-3">
                {data.finalContent}
              </p>

              {/* 标签 */}
              {data.tags && data.tags.length > 0 && (
                <div className="mb-3 text-sm">
                  {data.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="text-blue-400 hover:underline cursor-pointer ml-1"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              )}

              {/* 图片区域 */}
              <div className="mb-3">
                <ImageCarousel
                  photos={photos}
                  aspectRatio="aspect-video"
                  showArrows={true}
                  showPageIndicator={true}
                  showDots={false}
                  enableKeyboardNavigation={true}
                  enablePreloading={true}
                  placeholderIconSize="w-16 h-16"
                  className="rounded-2xl overflow-hidden"
                />
              </div>

              {/* 互动按钮区域 */}
              <div className="flex items-center justify-between max-w-md pt-2">
                <div className="flex items-center gap-1 text-gray-400 hover:text-blue-400 cursor-pointer">
                  <ChatBubbleOvalLeftIcon className="w-5 h-5" />
                  <span className="text-sm">234</span>
                </div>

                <div className="flex items-center gap-1 text-gray-400 hover:text-green-400 cursor-pointer">
                  <ArrowPathRoundedSquareIcon className="w-5 h-5" />
                  <span className="text-sm">67</span>
                </div>

                <div className="flex items-center gap-1 text-gray-400 hover:text-red-400 cursor-pointer">
                  <HeartIcon className="w-5 h-5" />
                  <span className="text-sm">1.8k</span>
                </div>

                <div className="text-gray-400 hover:text-blue-400 cursor-pointer">
                  <ShareIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
      <div className="flex gap-4 justify-center mt-4">
        <Button
          size="sm"
          variant="outline"
          disabled={data.status === "PUBLISHED"}
          onClick={() => {
            onPublish?.(data);
          }}
          className="hover:bg-red-600 text-white px-6 py-1 rounded-full text-sm font-medium"
        >
          {data.status === "PUBLISHED"
            ? t("button:published")
            : t("button:publish")}
        </Button>
        <Button
          size="sm"
          disabled={data.status === "FAVORITES" || data.status === "PUBLISHED"}
          variant="outline"
          onClick={() => {
            onSave?.(data);
          }}
          className="text-white hover:bg-surface-light px-6 py-1 rounded-full text-sm font-medium"
        >
          {data.status === "FAVORITES" ? t("button:saved") : t("button:save")}
        </Button>
      </div>
    </div>
  );
};

export default Twitter;
