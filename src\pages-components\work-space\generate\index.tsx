"use client";

import { RocketLaunchIcon } from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";
import { useCallback, useEffect } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";
import { useAppStore } from "@/store/useAppStore";

const Generate = () => {
  const { t } = useLanguage();
  const router = useRouter();
  const { setHeaderSlot } = useAppStore();
  const { currentRole } = useRoleStore();

  const onGenerate = useCallback(() => {
    // 检查当前角色是否有效
    if (!currentRole?.personaId) {
      toast.error(t("components:empty.description.noRoles"));
      return;
    }

    router.push("/workspace/generating");
  }, [currentRole?.personaId, t, router]);

  // 判断是否正在加载或没有可用角色
  const isDisabled = !currentRole?.personaId;

  useEffect(() => {
    setHeaderSlot(
      <Button
        className="focus:outline-none"
        onClick={onGenerate}
        disabled={isDisabled}
      >
        <RocketLaunchIcon className="size-4"></RocketLaunchIcon>
        {t("button:generateNow")}
      </Button>
    );
    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot, onGenerate, isDisabled, t]);

  return <div></div>;
};

export default Generate;
