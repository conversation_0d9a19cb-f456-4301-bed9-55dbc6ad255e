export const usePollingTask = () => {
  let timer: number | undefined = undefined;
  let timeoutTimer: number | undefined = undefined;

  const stop = () => {
    clearTimeout(timeoutTimer);
    clearInterval(timer);
  };
  return {
    /**
     *
     * @param task | function
     * @param interval | in milliseconds
     * @param timeout | in milliseconds
     * @param immediate
     */
    start({
      task,
      interval,
      timeout,
      immediate = false,
    }: {
      task: () => Promise<unknown>;
      interval: number;
      timeout: number;
      immediate?: boolean;
    }) {
      stop();

      if (immediate) {
        task();
      }

      timeoutTimer = window.setTimeout(() => {
        clearInterval(timer);
      }, timeout);

      timer = window.setInterval(async () => {
        await task();
      }, interval);
    },
    stop,
  };
};
