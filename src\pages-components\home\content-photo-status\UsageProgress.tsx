"use client";

import { Progress } from "@/components/ui/progress";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";

interface UsageProgressProps {
  used: number;
  total: number;
  label?: string;
}

const UsageProgress = ({ used, total, label }: UsageProgressProps) => {
  const { t } = useLanguage();
  const percentage = Math.round((used / total) * 100);

  return (
    <>
      <Text
        variant={TextVariant.BODY_SMALL}
        className="mb-4 flex justify-between items-center"
      >
        <span>{label || t("home:photoUsage.progressLabel")}</span>
        <span className="text-primary">
          {used}/{total}
        </span>
      </Text>
      <Progress value={percentage} className="h-3" />
    </>
  );
};

export default UsageProgress;
