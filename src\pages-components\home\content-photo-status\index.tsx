"use client";

import { useRouter } from "next/navigation";
import { useEffect, useMemo } from "react";
import { useLanguage } from "@/hooks/useLanguage";
import useGenerateStore from "@/store/generate";
import useRoleStore from "@/store/persona";
import PhotoUsageCard from "./PhotoUsageCard";
import StatCard from "./StatCard";
const ContentPhotoStatus = () => {
  const router = useRouter();
  const { t } = useLanguage();
  const { getGenerateStatistics, statistics } = useGenerateStore();
  const { currentRole } = useRoleStore();
  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getGenerateStatistics(currentRole.personaId);
    }
  }, [currentRole]);

  const contentStats = useMemo(() => {
    return [
      {
        name: t("home:contentStats.totalGenerated"),
        value: statistics.totalGenerated,
      },
      {
        name: t("home:contentStats.published"),
        value: statistics.published,
      },
      { name: t("button:saved"), value: statistics.favorites },
    ];
  }, [statistics, t]);

  const handleManagePhotos = () => {
    router.push("/camera-roll");
  };

  return (
    <div className="grid grid-cols-2 max-md:grid-cols-1 gap-4">
      <StatCard title={t("home:contentStats.title")} stats={contentStats} />
      <PhotoUsageCard
        title={t("home:photoUsage.title")}
        onManageClick={handleManagePhotos}
        buttonText={t("button:managePhotoLibrary")}
      />
    </div>
  );
};

export default ContentPhotoStatus;
