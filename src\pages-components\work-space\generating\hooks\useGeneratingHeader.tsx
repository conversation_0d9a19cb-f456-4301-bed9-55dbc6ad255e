import { ArrowLeftCircleIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import { useAppStore } from "@/store/useAppStore";

/**
 * 生成页面头部逻辑的自定义Hook
 *
 * 管理页面头部的返回按钮
 */
export const useGeneratingHeader = () => {
  const { t } = useLanguage();
  const { setHeaderSlot } = useAppStore();
  const router = useRouter();

  useEffect(() => {
    setHeaderSlot(
      <Button
        variant="secondary"
        size="sm"
        onClick={() => router.push("/workspace")}
      >
        <ArrowLeftCircleIcon className="size-4" />
        {t("button:backToWorkspace")}
      </Button>
    );

    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot, router, t]);
};
