"use client";

import {
  HomeIcon,
  UserCircleIcon,
  PhotoIcon,
  ChatBubbleLeftRightIcon,
  Cog8ToothIcon,
} from "@heroicons/react/24/solid";
import * as React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAccountStore } from "@/store/account";
import { NavMain } from "./NavMain";
import { NavUser } from "./NavUser";
import { TeamSwitcher } from "./Role";

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "",
    phone: "**********",
  },
  navMain: [
    {
      title: "route:home",
      url: "/",
      icon: HomeIcon,
    },
    {
      title: "route:creation",
      url: "/workspace",
      icon: UserCircleIcon,
    },
    {
      title: "route:cameraRoll",
      url: "/camera-roll",
      icon: PhotoIcon,
    },
    {
      title: "route:informationSource",
      url: "",
      icon: ChatBubbleLeftRightIcon,
      items: [
        {
          title: "route:builtinInformationSource",
          url: "/info/source",
        },
        {
          title: "route:customInformationSource",
          url: "/info/custom-source",
        },
        {
          title: "route:knowledgeBase",
          url: "/info/knowledge-base",
        },
      ],
    },
    {
      title: "route:settings",
      url: "",
      icon: Cog8ToothIcon,
      items: [
        {
          title: "route:personalSettings",
          url: "/setting/user",
        },
        {
          title: "route:aiAssistantSettings",
          url: "/setting/ai",
        },
        {
          title: "route:dataManagement",
          url: "/setting/data",
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAccountStore();
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent className="overflow-hidden">
        <ScrollArea className="flex-1 h-full">
          <div className="space-y-1 p-2">
            <NavMain items={data.navMain} />
          </div>
        </ScrollArea>
      </SidebarContent>
      <SidebarFooter className="!p-0">
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
