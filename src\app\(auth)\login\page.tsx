import dynamic from "next/dynamic";
import { PreloadWrapper } from "@/components/PreloadWrapper";

const LoginForm = dynamic(() => import("@/pages-components/account/Login"), {
  loading: () => <div>Loading...</div>,
});

export default function LoginPage() {
  return (
    <PreloadWrapper routes={["/", "/workspace"]}>
      <div className="bg-background flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
        <div className="w-full max-w-sm">
          <LoginForm />
        </div>
      </div>
    </PreloadWrapper>
  );
}
