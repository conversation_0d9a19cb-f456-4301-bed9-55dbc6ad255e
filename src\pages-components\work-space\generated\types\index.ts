import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { IGenerateList } from "@/services/api/generate";

/**
 * 生成结果页面相关的类型定义
 */

// 头部组件属性
export interface GeneratedHeaderProps {
  router: AppRouterInstance;
}

// 摘要组件属性
export interface GeneratedSummaryProps {
  contentCount: number;
  currentLanguage: string;
}

// 内容网格组件属性
export interface ContentGridProps {
  finalContent: IGenerateList[];
  currentLanguage: string;
  onPublish: (data: IGenerateList) => Promise<void>;
  onSave: (data: IGenerateList) => Promise<void>;
}

// 平台内容渲染器组件属性
export interface PlatformContentRendererProps {
  data: IGenerateList;
  currentLanguage: string;
  onPublish: (data: IGenerateList) => Promise<void>;
  onSave: (data: IGenerateList) => Promise<void>;
}

// 平台类型枚举
export enum PlatformType {
  XHS = "zh-CN",
  TWITTER = "en-US",
  INSTAGRAM = "instagram",
}

// 内容操作类型
export type ContentAction = "publish" | "save";

// 内容操作处理函数类型
export type ContentActionHandler = (data: IGenerateList) => Promise<void>;
