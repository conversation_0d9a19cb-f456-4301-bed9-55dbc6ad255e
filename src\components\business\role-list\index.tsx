import { TrashIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";
import { Loader } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { ProfileIcon } from "@/components/ProfileIcon";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import { cn } from "@/lib/utils";
import useRoleStore from "@/store/persona";
import { Card } from "../card";
import EmptyState, { EmptyType } from "../empty";

const RoleList = ({ onCreateRole }: { onCreateRole: () => void }) => {
  const { t } = useLanguage();
  const [deletingRoleId, setDeletingRoleId] = useState<string | null>(null);
  const [hoveredRoleId, setHoveredRoleId] = useState<string | null>(null);

  const { roleList, currentRole, setCurrentRole, setIsDefault, deleteRole } =
    useRoleStore();

  const handleDeleteRole = async (
    e: React.MouseEvent,
    personaId: string
    // roleName: string
  ) => {
    e.stopPropagation(); // 防止触发角色选择

    if (deletingRoleId === personaId) {
      return;
    }

    // 检查是否是当前选中的角色
    if (personaId === currentRole.personaId) {
      toast.error(t("components:roleList.cannotDeleteCurrentRole"));
      return;
    }

    try {
      setDeletingRoleId(personaId);
      await deleteRole(personaId);
      toast.success(t("components:roleList.deleteSuccess"));
    } catch (error) {
      console.error("Delete role failed:", error);
      toast.error(t("components:roleList.deleteFailed"));
    } finally {
      setDeletingRoleId(null);
    }
  };
  if (roleList.length === 0) {
    return (
      <EmptyState
        iconSize="sm"
        compact
        icon={<ProfileIcon seed="no-roles" size={40}></ProfileIcon>}
        type={EmptyType.NO_DATA}
        title={t("components:empty.noRoles")}
        description={t("components:empty.description.noRoles")}
        actions={[
          {
            label: t("components:empty.actions.create"),
            onClick: () => {
              onCreateRole();
            },
            variant: "default",
          },
        ]}
      />
    );
  }
  return (
    <div className="flex flex-col gap-2 py-4 pr-2.5">
      {roleList.map((role) => (
        <Card
          key={role.personaId}
          className={clsx(
            "cursor-pointer bg-background relative",
            role.personaId === currentRole.personaId
              ? "bg-primary-alpha-10 border-ring hover:bg-primary-alpha-10"
              : "hover:bg-surface-light"
          )}
        >
          <div
            className="flex gap-4 items-center"
            onMouseEnter={() => setHoveredRoleId(role.personaId)}
            onMouseLeave={() => setHoveredRoleId(null)}
            onClick={() => {
              setCurrentRole(role);
              setIsDefault(role.personaId);
            }}
          >
            <div className="w-10 h-10 rounded-full bg-surface-light flex items-center justify-center">
              <ProfileIcon seed={role.personaId} size={40}></ProfileIcon>
            </div>
            <div className="flex-1">
              <Text variant={TextVariant.BODY_MEDIUM}>{role.name}</Text>
              <Text
                variant={TextVariant.BODY_SMALL}
                className="text-text-secondary mt-1"
              >
                {role.mbti}
              </Text>
            </div>

            {/* 删除按钮 - hover 时显示，当前角色不显示 */}
            {role.personaId !== currentRole.personaId &&
              hoveredRoleId === role.personaId && (
                <button
                  onClick={(e) => handleDeleteRole(e, role.personaId)}
                  disabled={deletingRoleId === role.personaId}
                  className={cn(
                    "absolute top-1/2 right-2 -translate-y-1/2 p-1.5 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-lg transition-all duration-200 z-10",
                    "opacity-100 transform scale-100",
                    deletingRoleId === role.personaId &&
                      "opacity-50 cursor-not-allowed"
                  )}
                  title={t("components:roleList.deleteRole")}
                >
                  {deletingRoleId === role.personaId ? (
                    <Loader className="size-3 animate-spin" />
                  ) : (
                    <TrashIcon className="size-3" />
                  )}
                </button>
              )}
          </div>
        </Card>
      ))}
    </div>
  );
};

export default RoleList;
