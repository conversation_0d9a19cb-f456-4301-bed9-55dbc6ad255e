import { useState, useCallback, useEffect } from "react";

interface Photo {
  imageUrl: string;
  id: string;
}

interface UseImageCarouselProps {
  photos: Photo[];
  enableKeyboardNavigation?: boolean;
  enablePreloading?: boolean;
  preloadDelay?: number;
}

interface UseImageCarouselReturn {
  currentImage: number;
  currentImageIndex: number;
  hasMultipleImages: boolean;
  isCurrentImageError: boolean;
  goToPrevImage: () => void;
  goToNextImage: () => void;
  goToImage: (index: number) => void;
  handleImageError: (index: number) => void;
}

/**
 * 图片轮播自定义Hook
 *
 * 功能特性：
 * - 支持多图片轮播展示
 * - 键盘导航支持（左右箭头键）
 * - 图片预加载和错误处理
 * - 可配置的预加载延迟
 *
 * @param photos - 图片数组
 * @param enableKeyboardNavigation - 是否启用键盘导航，默认true
 * @param enablePreloading - 是否启用图片预加载，默认true
 * @param preloadDelay - 预加载延迟时间（毫秒），默认100
 * @returns 轮播相关的状态和方法
 */
export const useImageCarousel = ({
  photos,
  enableKeyboardNavigation = true,
  enablePreloading = true,
  preloadDelay = 100,
}: UseImageCarouselProps): UseImageCarouselReturn => {
  const [currentImage, setCurrentImage] = useState(0);
  const [imageLoadError, setImageLoadError] = useState<Set<number>>(new Set());

  // 图片相关的计算属性
  const hasMultipleImages = photos.length > 1;
  const currentImageIndex = Math.min(currentImage, photos.length - 1);

  // 图片导航函数
  const goToPrevImage = useCallback(() => {
    setCurrentImage((prev) => (prev > 0 ? prev - 1 : photos.length - 1));
  }, [photos.length]);

  const goToNextImage = useCallback(() => {
    setCurrentImage((prev) => (prev < photos.length - 1 ? prev + 1 : 0));
  }, [photos.length]);

  const goToImage = useCallback((index: number) => {
    setCurrentImage(index);
  }, []);

  // 图片错误处理
  const handleImageError = useCallback((index: number) => {
    setImageLoadError((prev) => new Set(prev).add(index));
  }, []);

  // 检查当前图片是否加载失败
  const isCurrentImageError = imageLoadError.has(currentImageIndex);

  // 键盘导航支持
  useEffect(() => {
    if (!hasMultipleImages || !enableKeyboardNavigation) {
      return;
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body) {
        return;
      } // 只在没有焦点元素时响应

      switch (event.key) {
        case "ArrowLeft":
          event.preventDefault();
          goToPrevImage();
          break;
        case "ArrowRight":
          event.preventDefault();
          goToNextImage();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [
    hasMultipleImages,
    enableKeyboardNavigation,
    goToPrevImage,
    goToNextImage,
  ]);

  // 图片预加载
  useEffect(() => {
    if (!enablePreloading || photos.length <= 1) {
      return;
    }

    const preloadImages = () => {
      photos.forEach((photo, index) => {
        if (index !== currentImageIndex) {
          const img = new Image();
          img.src = photo.imageUrl;
        }
      });
    };

    // 延迟预加载，避免影响当前图片加载
    const timer = setTimeout(preloadImages, preloadDelay);
    return () => clearTimeout(timer);
  }, [photos, currentImageIndex, enablePreloading, preloadDelay]);

  // 当photos数组变化时重置当前图片索引
  useEffect(() => {
    if (currentImage >= photos.length) {
      setCurrentImage(0);
    }
  }, [photos.length, currentImage]);

  return {
    currentImage,
    currentImageIndex,
    hasMultipleImages,
    isCurrentImageError,
    goToPrevImage,
    goToNextImage,
    goToImage,
    handleImageError,
  };
};
