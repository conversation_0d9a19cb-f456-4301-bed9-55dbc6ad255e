import type { UploadConfig } from "../types";
import { FILE_CONFIGS } from "./fileConfig";
import { getFileCategory } from "./fileUtils";
import { isFileAllowed } from "./uploadConfig";

// 验证文件
export const validateFile = (file: File, config?: UploadConfig) => {
  const errors: string[] = [];
  const category = getFileCategory(file);

  // 如果提供了配置，检查文件是否被允许
  if (config && !isFileAllowed(file, config)) {
    errors.push("不支持的文件类型");
    return errors;
  }

  // 如果没有配置，使用默认验证
  if (!config && !category) {
    errors.push("不支持的文件类型");
    return errors;
  }

  // 检查文件大小
  if (category) {
    let maxSize: number;

    if (config?.maxSize) {
      maxSize =
        config.maxSize[category as keyof typeof config.maxSize] ||
        FILE_CONFIGS[category as keyof typeof FILE_CONFIGS].maxSize;
    } else {
      maxSize = FILE_CONFIGS[category as keyof typeof FILE_CONFIGS].maxSize;
    }

    if (file.size > maxSize) {
      errors.push(`文件大小不能超过 ${maxSize / 1024 / 1024}MB`);
    }
  }

  return errors;
};
