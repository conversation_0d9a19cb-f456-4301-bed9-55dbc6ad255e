import { useLanguage } from "@/hooks/useLanguage";
import { IPersona } from "@/services/api/persona";
import useRoleStore from "@/store/persona";
import RoleForm from "./RoleForm";
import { DialogModal } from "./index";

const ModalAddRole = ({
  open,
  close,
}: {
  open: boolean;
  close: () => void;
}) => {
  const { createRole } = useRoleStore();
  const { t } = useLanguage();
  const handleCreate = async (data: IPersona) => {
    await createRole(data);
    close();
  };

  return (
    <DialogModal
      open={open}
      openChange={close}
      className="w-[36rem] bg-surface"
      title={t("modal:addRole.title")}
    >
      <RoleForm onConfirm={handleCreate} onclose={close} />
    </DialogModal>
  );
};
export default ModalAddRole;
