import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
// import { ENV } from "@/config/env";
import { SUPPORTED_LANGUAGES } from "@/lib/i18n";
import i18n from "@/lib/i18n";
const getLang = () => {
  // 检查是否在浏览器环境中
  if (process.env.NODE_ENV === "development") {
    return SUPPORTED_LANGUAGES["zh-CN"];
  }
  if (typeof window !== "undefined" && location.host.includes("xhsperson")) {
    return SUPPORTED_LANGUAGES["zh-CN"];
  } else {
    return SUPPORTED_LANGUAGES["en-US"];
  }
};
interface ConfigStore {
  navPosition: "left" | "top" | "all"; // 导航位置- 左侧、顶部、全部
  theme: "light" | "dark"; // 顶栏主题-亮色、暗色
  language: SUPPORTED_LANGUAGES; // 语言-中文、英文、日文
  setLanguage: (lang: SUPPORTED_LANGUAGES) => void;
  setTheme: (theme: "light" | "dark") => void;
}
const useHomeStore = create<ConfigStore>()(
  immer(
    (set): ConfigStore => ({
      navPosition: "left",
      theme: "dark",
      language: getLang(), // SUPPORTED_LANGUAGES["zh-CN"],
      setLanguage(lang) {
        set({
          language: lang,
        });
        // 同步更新 i18n 语言
        if (i18n.isInitialized && i18n.language !== lang) {
          i18n.changeLanguage(lang);
        }
      },
      setTheme(theme) {
        set({
          theme: theme,
        });
        // 同步更新 DOM 主题属性
        if (typeof window !== "undefined") {
          document.documentElement.setAttribute("data-theme", theme);
          localStorage.setItem("theme", theme);
        }
      },
    })
  )
);

export default useHomeStore;
