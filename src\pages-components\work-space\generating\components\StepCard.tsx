"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useMemo, useState, useEffect } from "react";
import { Card } from "@/components/business/card";
import { useLanguage } from "@/hooks/useLanguage";
import { StepData } from "../hooks/useGeneratingSteps";
import { Processing } from "../index";
import { TypewriterText } from "./TypewriterText";

interface StepCardProps {
  step: StepData;
}

/**
 * StepCard 组件 - 生成步骤卡片
 *
 * 显示单个生成步骤，包含标题和详情的逐字显示动画
 */
export const StepCard: React.FC<StepCardProps> = ({ step }) => {
  const [completedDetails, setCompletedDetails] = useState<string[]>([]);
  const [currentDetailIndex, setCurrentDetailIndex] = useState(0);
  const [showDetails, setShowDetails] = useState(false);
  const { t } = useLanguage();

  // 当步骤状态变为完成时，开始显示详情
  useEffect(() => {
    if (
      step.stage === Processing.COMPLETED &&
      step.details &&
      Array.isArray(step.details) &&
      !showDetails // 只有当还没有开始显示时才初始化
    ) {
      setShowDetails(true);
      setCompletedDetails([]);
      setCurrentDetailIndex(0);
    }
  }, [step.stage, step.details, showDetails]);

  const handleDetailComplete = () => {
    if (
      !step.details ||
      !Array.isArray(step.details) ||
      step.details.length === 0
    ) {
      return;
    }

    const currentDetail = step.details[currentDetailIndex];

    // 添加当前完成的详情到已完成列表
    if (!completedDetails.includes(currentDetail)) {
      setCompletedDetails((prev) => [...prev, currentDetail]);
    }

    // 移动到下一个详情
    if (currentDetailIndex < step.details.length - 1) {
      setCurrentDetailIndex((prev) => prev + 1);
    }
  };

  // if (!isVisible) {
  //   return null;
  // }
  const Text = useMemo(() => {
    switch (step.stage) {
      case Processing.INIT:
        return <span>{t("workspace:notStarted")}...</span>;
      case Processing.PROCESSING:
        return (
          <span className="text-primary-300">
            <span className="spin360 text-[16px]">⏳</span>
            {t("workspace:generators")}
          </span>
        );
      case Processing.COMPLETED:
        return (
          <span className="text-green-400">✅ {t("workspace:completed")}</span>
        );
      default:
        return <span>{t("workspace:notStarted")}</span>;
    }
  }, [step, t]);
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <Card className="bg-background">
          <div>
            <p className="text-base font-medium flex justify-between">
              <span>{step.title}</span>
              {/* 三个状态，未开始，生成中，已完成 */}
              <span className="text-text-secondary">{Text}</span>
            </p>
            {step.isOutput ? (
              <div className="space-y-2 mt-4">
                <div className="text-sm tracking-wider text-text-secondary">
                  {step.details}
                </div>
              </div>
            ) : null}
            <AnimatePresence>
              {showDetails && step.details && Array.isArray(step.details) && (
                <motion.div
                  className="space-y-2 mt-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  {/* 显示已完成的详情 */}
                  {completedDetails.map((detail, index) => (
                    <div
                      key={`completed-${index}`}
                      className="text-sm tracking-wider text-text-secondary"
                    >
                      {detail}
                    </div>
                  ))}

                  {/* 显示当前正在打字的详情 */}
                  {step.stage === Processing.COMPLETED &&
                    currentDetailIndex < step.details.length &&
                    !completedDetails.includes(
                      step.details[currentDetailIndex]
                    ) && (
                      <TypewriterText
                        key={`current-${currentDetailIndex}`}
                        text={step.details[currentDetailIndex]}
                        delay={0.3}
                        speed={50}
                        onComplete={handleDetailComplete}
                        className="text-sm tracking-wider text-text-secondary"
                      />
                    )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </Card>
      </Card>
    </motion.div>
  );
};
