"use client";

import { useEffect, useState } from "react";

interface TypewriterTextProps {
  text: string;
  delay?: number;
  speed?: number;
  onComplete?: () => void;
  className?: string;
  showCursor?: boolean;
}

/**
 * TypewriterText 组件 - 逐字显示文本效果
 *
 * @param text - 要显示的文本
 * @param delay - 开始延迟时间（秒）
 * @param speed - 打字速度（毫秒）
 * @param onComplete - 完成回调
 * @param className - 自定义样式类
 * @param showCursor - 是否显示光标
 */
export const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  delay = 0,
  speed = 100,
  onComplete,
  className = "",
  showCursor = true,
}) => {
  const [displayedText, setDisplayedText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    // 延迟开始
    const startTimeout = setTimeout(() => {
      setHasStarted(true);
    }, delay * 1000);

    return () => clearTimeout(startTimeout);
  }, [delay]);

  useEffect(() => {
    if (!hasStarted || currentIndex >= text.length) {
      if (currentIndex >= text.length && onComplete) {
        onComplete();
      }
      return;
    }

    const timeout = setTimeout(() => {
      setDisplayedText((prev) => prev + text[currentIndex]);
      setCurrentIndex((prev) => prev + 1);
    }, speed);

    return () => clearTimeout(timeout);
  }, [hasStarted, currentIndex, text, speed, onComplete]);

  // 重置状态当文本改变时
  useEffect(() => {
    setDisplayedText("");
    setCurrentIndex(0);
    setHasStarted(false);
  }, [text]);

  return (
    <div className={`${className} tracking-wider`}>
      {displayedText}
      {showCursor && hasStarted && currentIndex < text.length && (
        <span className="animate-pulse">|</span>
      )}
    </div>
  );
};
