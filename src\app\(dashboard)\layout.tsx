"use client";
import { useEffect } from "react";
import Layout from "@/components/business/layout";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";
import { Toaster } from "@/components/ui/sonner";
import { useAccountStore } from "@/store/account";
import useRoleStore from "@/store/persona";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { fetchCurrentUser } = useAccountStore();
  const { getRoleList } = useRoleStore();
  useEffect(() => {
    fetchCurrentUser();
    getRoleList();
  }, []);
  return (
    <>
      <Toaster />
      <Layout>{children}</Layout>
      <PerformanceMonitor />
    </>
  );
}
