"use client";

import clsx from "clsx";
import { Loader } from "lucide-react";
import { useMemo, useState } from "react";
import { ProfileIcon } from "@/components/ProfileIcon";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImageCarousel } from "@/components/ui/ImageCarousel";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useRoleStore from "@/store/persona";

interface XhsProps {
  /** 额外的 CSS 类名 */
  className?: string;
  /** 生成的内容数据 */
  data: IGenerateList;
  /** 发布按钮点击事件 */
  onPublish?: (data: IGenerateList) => Promise<void>;
  onSave?: (data: IGenerateList) => Promise<void>;
  isShowButton?: boolean;
}

/**
 * 小红书风格的内容卡片组件
 *
 * 功能特性：
 * - 支持多图片轮播展示
 * - 键盘导航支持（左右箭头键）
 * - 图片预加载和错误处理
 * - 响应式设计
 * - 无障碍访问支持
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */

const Xhs = ({
  className,
  data,
  onPublish,
  onSave,
  isShowButton = true,
}: XhsProps) => {
  const { currentRole } = useRoleStore();
  const { t } = useLanguage();
  const [isFetching, setIsFetching] = useState(false);

  // 图片相关的计算属性
  const photos = useMemo(() => data.photos || [], [data.photos]);

  // 格式化日期
  const formattedDate = useMemo(() => {
    return new Date(data.createdAt || new Date()).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }, [data.createdAt]);
  return (
    <div>
      <Card
        className={`w-full mx-auto p-4 border-border hover:border-ring  ${className}`}
      >
        {/* 用户信息头部 */}
        <div className="flex items-center justify-between ">
          <div className="flex items-center gap-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src="" alt="Emma" />
              <AvatarFallback className="bg-red-500 text-white font-semibold">
                <ProfileIcon
                  seed={currentRole.personaId}
                  size={60}
                ></ProfileIcon>
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-semibold text-white">{currentRole.name}</div>
              <div className="text-sm text-gray-400">{formattedDate}</div>
            </div>
          </div>
          <Button
            size="sm"
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium"
          >
            关注
          </Button>
        </div>

        {/* 图片区域 */}
        <div className="px-4 pb-3">
          <ImageCarousel
            photos={photos}
            aspectRatio="aspect-[4/3]"
            showArrows={true}
            showPageIndicator={true}
            showDots={true}
            enableKeyboardNavigation={true}
            enablePreloading={true}
          />
        </div>

        {/* 内容区域 */}
        <div className="px-4 pb-4">
          {/* 标题 */}
          {data.title && (
            <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
              {data.title}
            </h3>
          )}

          {/* 描述 */}
          {data.finalContent && (
            <p className="text-gray-300 text-sm leading-relaxed mb-4">
              {data.finalContent}
            </p>
          )}

          {/* 标签 */}
          {data.tags && data.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {data.tags.slice(0, 6).map((item, index) => (
                <Badge
                  key={`${item}-${index}`}
                  variant="secondary"
                  className="bg-red-500/20 text-red-400 border-red-500/30 text-xs hover:bg-red-500/30 transition-colors duration-200"
                >
                  #{item}
                </Badge>
              ))}
              {data.tags.length > 6 && (
                <Badge
                  variant="secondary"
                  className="bg-gray-500/20 text-gray-400 border-gray-500/30 text-xs"
                >
                  +{data.tags.length - 6}
                </Badge>
              )}
            </div>
          )}
        </div>
      </Card>
      {isShowButton && (
        <div className="flex gap-4 justify-center mt-4">
          <Button
            size="sm"
            variant="outline"
            disabled={data.status === "PUBLISHED" || isFetching}
            onClick={async () => {
              setIsFetching(true);
              await onPublish?.(data);
              setIsFetching(false);
            }}
            className="hover:bg-red-600 text-white px-6 py-1 rounded-full text-sm font-medium"
          >
            {isFetching && <Loader className="size-4 spin360"></Loader>}
            {data.status === "PUBLISHED"
              ? t("button:published")
              : t("button:publish")}
          </Button>
          <Button
            size="sm"
            disabled={
              data.status === "FAVORITES" ||
              data.status === "PUBLISHED" ||
              isFetching
            }
            variant="outline"
            onClick={async () => {
              setIsFetching(true);
              await onSave?.(data);
              setIsFetching(false);
            }}
            className={clsx(
              "text-white hover:bg-surface-light px-6 py-1 rounded-full text-sm font-medium"
            )}
          >
            {isFetching && <Loader className="size-4 spin360"></Loader>}
            {data.status === "FAVORITES" ? t("button:saved") : t("button:save")}
          </Button>
        </div>
      )}
    </div>
  );
};

export default Xhs;
