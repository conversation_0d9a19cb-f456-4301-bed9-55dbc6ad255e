/**
 * 分片上传工具函数
 */

// 分片上传配置常量
export const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB 每个分片
export const MAX_CONCURRENT_UPLOADS = 3; // 最大并发上传数
export const MAX_RETRY_ATTEMPTS = 3; // 最大重试次数

/**
 * 将文件分割成多个分片
 */
export const splitFileIntoParts = (
  file: File,
  chunkSize: number = CHUNK_SIZE
): Blob[] => {
  const chunks: Blob[] = [];
  let start = 0;

  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    chunks.push(file.slice(start, end));
    start = end;
  }

  return chunks;
};

/**
 * 上传单个分片（带重试机制）
 */
export const uploadPartWithRetry = async (
  chunk: Blob,
  presignedUrl: string,
  signal?: AbortSignal,
  maxRetries: number = MAX_RETRY_ATTEMPTS
): Promise<string> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      if (signal?.aborted) {
        throw new Error("Upload aborted");
      }

      const response = await fetch(presignedUrl, {
        method: "PUT",
        body: chunk,
        signal,
        headers: {
          "Content-Type": "application/octet-stream",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 从响应头中获取 ETag
      const etag = response.headers.get("ETag");
      if (!etag) {
        throw new Error("ETag not found in response headers");
      }

      return etag.replace(/"/g, ""); // 移除引号
    } catch (error) {
      lastError = error as Error;

      // 如果是取消操作，直接抛出错误
      if (error instanceof Error && error.name === "AbortError") {
        throw error;
      }

      // 如果不是最后一次尝试，等待一段时间后重试
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最大5秒
        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }
    }
  }

  throw lastError!;
};

/**
 * 计算文件的 MD5 哈希（可选，用于完整性验证）
 */
export const calculateMD5 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer;
        const hashBuffer = await crypto.subtle.digest("MD5", arrayBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray
          .map((b) => b.toString(16).padStart(2, "0"))
          .join("");
        resolve(hashHex);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => reject(new Error("Failed to read file"));
    reader.readAsArrayBuffer(file);
  });
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) {
    return "0 Bytes";
  }

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * 计算上传速度
 */
export const calculateUploadSpeed = (
  uploadedBytes: number,
  startTime: number
): string => {
  const elapsedTime = (Date.now() - startTime) / 1000; // 秒
  const speed = uploadedBytes / elapsedTime; // 字节/秒
  return formatFileSize(speed) + "/s";
};

/**
 * 估算剩余时间
 */
export const estimateRemainingTime = (
  uploadedBytes: number,
  totalBytes: number,
  startTime: number,
  t?: (key: string, options?: any) => string
): string => {
  // 防止无效参数
  if (
    !uploadedBytes ||
    !totalBytes ||
    !startTime ||
    uploadedBytes <= 0 ||
    totalBytes <= 0
  ) {
    return "";
  }

  const elapsedTime = (Date.now() - startTime) / 1000;

  // 防止除零和负数
  if (elapsedTime <= 0 || uploadedBytes >= totalBytes) {
    return "";
  }

  const speed = uploadedBytes / elapsedTime;
  const remainingBytes = totalBytes - uploadedBytes;

  // 防止速度为零或负数
  if (speed <= 0 || remainingBytes <= 0) {
    return "";
  }

  const remainingTime = remainingBytes / speed;

  // 防止无限大或负数时间
  if (!isFinite(remainingTime) || remainingTime <= 0) {
    return "";
  }

  if (remainingTime < 60) {
    const seconds = Math.round(remainingTime);
    return t
      ? t("upload:progress.time.seconds", { count: seconds })
      : `${seconds}秒`;
  } else if (remainingTime < 3600) {
    const minutes = Math.round(remainingTime / 60);
    return t
      ? t("upload:progress.time.minutes", { count: minutes })
      : `${minutes}分钟`;
  } else {
    const hours = Math.round(remainingTime / 3600);
    return t
      ? t("upload:progress.time.hours", { count: hours })
      : `${hours}小时`;
  }
};
