import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";
export interface IConfig {
  id?: string; // 配置唯一标识
  userId?: string; // 用户ID
  personaId?: string; // 人设ID
  writingStyle:
    | "warmAndCute"
    | "professionalAndStrict"
    | "livelyAndFun"
    | "literaryAndFresh";
  contentLength: "short" | "medium" | "long";
  writingFields?: string[];
  createdAt?: string;
  updatedAt?: string;
}
// ai/setting
export const aiSettingApi = async (
  data: IConfig
): Promise<ApiResponse<{ success: boolean }>> => {
  return await api.post(`/ai/config`, data);
};

// ai/config
export const getAiConfigApi = async (
  personaId: string
): Promise<
  ApiResponse<{
    config: IConfig;
    message: string;
  }>
> => {
  return await api.post(`/ai/get`, { personaId });
};
