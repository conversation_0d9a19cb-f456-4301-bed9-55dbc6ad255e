import { DialogModal } from "@/components/business/modal";
import { DocumentUpload } from "@/components/business/upload/DocumentUpload";
import {
  FileData,
  UploadCompleteInfo,
} from "@/components/business/upload/types";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";

const ModalUpload = ({
  open,
  close,
  handleComplete,
}: {
  open: boolean;
  close: () => void;
  handleComplete: (files: FileData[]) => void;
}) => {
  const { roleList } = useRoleStore();
  const { t } = useLanguage();

  // 适配新的回调格式
  const onUploadComplete = async (info: UploadCompleteInfo) => {
    // 开发环境下显示文件总数信息
    if (process.env.NODE_ENV === "development") {
      console.warn(
        `文档上传完成: 总共 ${info.totalCount} 个文件，已完成 ${info.completedCount} 个`
      );
    }

    // 只有当所有文件都上传完成时才调用 handleComplete 和关闭弹窗
    if (info.totalCount === info.completedCount) {
      await handleComplete(info.files);
      setTimeout(() => {
        close();
      }, 0);
    }
  };
  if (!open) {
    return null;
  }
  return (
    <DialogModal
      open={open}
      openChange={close}
      className="w-[40rem] bg-surface"
      title={t("information:knowledgeBase.title")}
    >
      <DocumentUpload
        onUploadComplete={onUploadComplete}
        disabled={roleList.length === 0}
        disabledMessage={
          <div>
            <div>{t("upload:disabled.document")}</div>
            <div className="text-sm mt-4">
              {t("components:empty.description.noRoles")}
            </div>
          </div>
        }
      ></DocumentUpload>
    </DialogModal>
  );
};
export default ModalUpload;
