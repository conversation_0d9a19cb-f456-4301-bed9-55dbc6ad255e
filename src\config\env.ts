/**
 * 环境变量配置
 * 统一管理所有环境变量，提供类型安全和默认值
 */

// 基础环境配置
export const ENV = {
  NODE_ENV: process.env.NODE_ENV,
  IS_DEVELOPMENT: process.env.NODE_ENV === "development",
  IS_PRODUCTION: process.env.NODE_ENV === "production",
} as const;

// API 配置
export const API_ENV = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL,
  VERSION: process.env.NEXT_PUBLIC_API_VERSION || "v1",
  TIMEOUT: 10000,
} as const;

// 缓存配置
export const CACHE_ENV = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
  MAX_MEMORY_ITEMS: 100, // 内存缓存最大条目数
  CLEANUP_INTERVAL: 60 * 1000, // 清理间隔1分钟
  ENABLE_STORAGE_CACHE: true, // 是否启用持久化缓存
  ENABLE_CACHE_DEBUG:
    process.env.NEXT_PUBLIC_ENABLE_CACHE_DEBUG === "true" || ENV.IS_DEVELOPMENT,
} as const;

// 功能开关
export const FEATURE_FLAGS = {
  ENABLE_DEBUG:
    process.env.NEXT_PUBLIC_ENABLE_DEBUG === "true" || ENV.IS_DEVELOPMENT,
  LOG_LEVEL:
    (process.env.NEXT_PUBLIC_LOG_LEVEL as
      | "debug"
      | "info"
      | "warn"
      | "error") || "info",
} as const;

// 第三方服务配置
export const THIRD_PARTY_ENV = {
  ANALYTICS_ID: process.env.NEXT_PUBLIC_ANALYTICS_ID,
  SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
} as const;

// 开发工具配置
export const DEV_ENV = {
  ANALYZE: process.env.ANALYZE === "true",
  BUNDLE_ANALYZE: process.env.BUNDLE_ANALYZE === "true",
} as const;

// 验证必需的环境变量
export function validateEnv() {
  const requiredEnvVars: string[] = [
    "NEXT_PUBLIC_APP_URL",
    // 在这里添加必需的环境变量
  ];

  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  );

  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingEnvVars.join(", ")}`
    );
  }
}

// 获取完整的环境配置
export function getEnvConfig() {
  return {
    env: ENV,
    api: API_ENV,
    cache: CACHE_ENV,
    features: FEATURE_FLAGS,
    thirdParty: THIRD_PARTY_ENV,
    dev: DEV_ENV,
  };
}

// 开发环境下打印配置信息
if (ENV.IS_DEVELOPMENT) {
  console.log("Environment Configuration:", getEnvConfig());
}

// 导出所有配置
const envConfig = {
  ENV,
  API_ENV,
  CACHE_ENV,
  FEATURE_FLAGS,
  THIRD_PARTY_ENV,
  DEV_ENV,
  validateEnv,
  getEnvConfig,
};

export default envConfig;
