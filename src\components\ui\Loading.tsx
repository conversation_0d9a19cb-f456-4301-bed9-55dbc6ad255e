"use client";

import React from "react";
import { cn } from "@/utils";

interface LoadingProps {
  size?: "sm" | "md" | "lg";
  variant?: "dots" | "spinner" | "skeleton" | "pulse";
  className?: string;
  text?: string;
}

interface SkeletonProps {
  className?: string;
  lines?: number;
  avatar?: boolean;
}

// 加载点动画 - 保持原有实现用于 dots 变体
const LoadingDots: React.FC<{ size: string; className?: string }> = ({
  size,
  className,
}) => (
  <div className={cn("flex items-center space-x-1", className)}>
    <div className={cn("animate-pulse bg-primary rounded-full", size)} />
    <div
      className={cn("animate-pulse bg-primary rounded-full", size)}
      style={{ animationDelay: "0.2s" }}
    />
    <div
      className={cn("animate-pulse bg-primary rounded-full", size)}
      style={{ animationDelay: "0.4s" }}
    />
  </div>
);

// 脉冲动画 - 保持原有实现用于 pulse 变体
const LoadingPulse: React.FC<{ size: string; className?: string }> = ({
  size,
  className,
}) => (
  <div className={cn("animate-pulse bg-primary/20 rounded", size, className)} />
);

/**
 * 统一的加载组件
 *
 * @example
 * ```tsx
 * <Loading variant="dots" size="md" text="加载中..." />
 * <Loading variant="spinner" size="lg" />
 * <Loading variant="skeleton" />
 * ```
 */
export const Loading: React.FC<LoadingProps> = ({
  size = "md",
  variant = "dots",
  className,
  text,
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  };

  const dotSizeClasses = {
    sm: "w-1 h-1",
    md: "w-2 h-2",
    lg: "w-3 h-3",
  };

  const renderLoading = () => {
    switch (variant) {
      case "dots":
        return (
          <LoadingDots size={dotSizeClasses[size]} className={className} />
        );

      case "pulse":
        return <LoadingPulse size={sizeClasses[size]} className={className} />;
      case "skeleton":
        return <Skeleton className={className} />;
      default:
        return (
          <LoadingDots size={dotSizeClasses[size]} className={className} />
        );
    }
  };

  if (text) {
    return (
      <div className="flex items-center space-x-3">
        {renderLoading()}
        <span className="text-text-secondary">{text}</span>
      </div>
    );
  }

  return renderLoading();
};

/**
 * 骨架屏组件
 *
 * @example
 * ```tsx
 * <Skeleton lines={3} avatar />
 * <Skeleton className="h-32 w-full" />
 * ```
 */
export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  lines = 1,
  avatar = false,
}) => {
  return (
    <div className={cn("animate-pulse", className)}>
      {avatar && (
        <div className="flex items-start space-x-3 mb-3">
          <div className="w-10 h-10 bg-surface-light rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-surface-light rounded w-3/4" />
            <div className="h-3 bg-surface-light rounded w-1/2" />
          </div>
        </div>
      )}

      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              "h-4 bg-surface-light rounded",
              index === lines - 1 && lines > 1 && "w-3/4", // 最后一行稍短
              lines === 1 && "w-full"
            )}
          />
        ))}
      </div>
    </div>
  );
};

/**
 * 页面级加载组件
 */
export const PageLoading: React.FC<{ text?: string }> = ({
  text = "加载中...",
}) => (
  <div className="min-h-screen flex items-center justify-center bg-background">
    <div className="text-center">
      <Loading variant="spinner" size="lg" />
      <p className="mt-4 text-text-secondary">{text}</p>
    </div>
  </div>
);

/**
 * 内容区域加载组件
 */
export const ContentLoading: React.FC<{
  text?: string;
  className?: string;
}> = ({ text = "加载中...", className }) => (
  <div
    className={cn("flex flex-col items-center justify-center py-12", className)}
  >
    <Loading variant="dots" size="md" />
    <p className="mt-3 text-text-secondary text-sm">{text}</p>
  </div>
);

/**
 * 按钮加载状态
 */
export const ButtonLoading: React.FC<{ size?: "sm" | "md" | "lg" }> = ({
  size = "md",
}) => <Loading variant="dots" size={size} />;

/**
 * 卡片骨架屏
 */
export const CardSkeleton: React.FC<{
  avatar?: boolean;
  lines?: number;
  className?: string;
}> = ({ avatar = false, lines = 3, className }) => (
  <div className={cn("p-4 border border-border rounded-lg", className)}>
    <Skeleton avatar={avatar} lines={lines} />
  </div>
);

/**
 * 列表骨架屏
 */
export const ListSkeleton: React.FC<{
  items?: number;
  avatar?: boolean;
  className?: string;
}> = ({ items = 3, avatar = false, className }) => (
  <div className={cn("space-y-4", className)}>
    {Array.from({ length: items }).map((_, index) => (
      <CardSkeleton key={index} avatar={avatar} lines={2} />
    ))}
  </div>
);

/**
 * 表格骨架屏
 */
export const TableSkeleton: React.FC<{
  rows?: number;
  columns?: number;
  className?: string;
}> = ({ rows = 5, columns = 4, className }) => (
  <div className={cn("space-y-3", className)}>
    {/* 表头 */}
    <div
      className="grid gap-4"
      style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
    >
      {Array.from({ length: columns }).map((_, index) => (
        <div key={index} className="h-4 bg-surface-light rounded" />
      ))}
    </div>

    {/* 表格行 */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div
        key={rowIndex}
        className="grid gap-4"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div key={colIndex} className="h-4 bg-surface-light rounded" />
        ))}
      </div>
    ))}
  </div>
);

// 导出所有组件
export default Loading;
