"use client";

import { Card } from "@/components/business/card";
import { Text, TextVariant } from "@/components/ui/Text";

interface StatItem {
  name: string;
  value: number;
}

interface StatCardProps {
  title: string;
  stats: StatItem[];
}

const StatCard = ({ title, stats }: StatCardProps) => {
  return (
    <Card>
      <Text variant={TextVariant.H4} className="mb-4">
        {title}
      </Text>
      <div className="grid gap-4">
        {stats.map((stat) => (
          <Card key={stat.name} className="bg-surface-light rounded-md py-3">
            <Text
              variant={TextVariant.BODY_LARGE}
              className="text-primary font-bold"
            >
              {stat.value}
            </Text>
            <Text variant={TextVariant.CAPTION} className="text-text-tertiary">
              {stat.name}
            </Text>
          </Card>
        ))}
      </div>
    </Card>
  );
};

export default StatCard;
