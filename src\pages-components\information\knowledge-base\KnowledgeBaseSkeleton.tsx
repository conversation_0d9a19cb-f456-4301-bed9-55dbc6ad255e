import { Card } from "@/components/business/card";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * 知识库页面骨架屏组件集合
 *
 * 这个文件包含了知识库页面的所有骨架屏组件：
 *
 * 1. KnowledgeBaseSkeleton - 完整页面骨架屏
 *    - 包含标题区域和文档列表
 *    - 支持配置文档卡片数量
 *
 * 2. DocumentCardSkeleton - 单个文档卡片骨架屏
 *    - 模拟DocumentPreview组件的结构
 *    - 包含文档图标、文件名、大小、状态、删除按钮等元素
 *
 * 使用示例：
 * ```tsx
 * // 完整页面加载
 * <KnowledgeBaseSkeleton documentCount={5} />
 *
 * // 单个卡片
 * <DocumentCardSkeleton />
 * ```
 */

interface KnowledgeBaseSkeletonProps {
  /** 文档卡片数量 */
  documentCount?: number;
}

/**
 * 知识库页面骨架屏组件
 * 包含标题区域和文档列表的加载状态
 */
const KnowledgeBaseSkeleton = ({
  documentCount = 3,
}: KnowledgeBaseSkeletonProps) => {
  return (
    <Card>
      {/* 标题区域骨架屏 */}
      <div className="mb-4 flex items-center justify-between">
        {/* 主标题骨架屏 */}
        <Skeleton className="h-7 w-32" />
        {/* 提示文本骨架屏 */}
        <Skeleton className="h-4 w-40" />
      </div>

      {/* 文档列表骨架屏 */}
      <div className="grid gap-4">
        <div className="flex gap-4 flex-col">
          {Array.from({ length: documentCount }).map((_, index) => (
            <DocumentCardSkeleton key={index} />
          ))}
        </div>
      </div>
    </Card>
  );
};

/**
 * 单个文档卡片骨架屏组件
 * 模拟DocumentPreview组件的结构和布局
 */
export const DocumentCardSkeleton = () => {
  return (
    <Card className="hover:border-ring">
      <div className="flex items-center gap-4">
        {/* 文档图标骨架屏 */}
        <div className="shrink-0">
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>

        {/* 文档信息区域 */}
        <div className="flex-1 min-w-0">
          {/* 文件名骨架屏 */}
          <Skeleton className="h-5 w-48 mb-2" />

          {/* 文件详情行 */}
          <div className="flex items-center gap-4 text-sm">
            {/* 文件大小骨架屏 */}
            <Skeleton className="h-4 w-16" />
            {/* 上传时间骨架屏 */}
            <Skeleton className="h-4 w-32" />
            {/* 状态标签骨架屏 */}
            <Skeleton className="h-5 w-20 rounded-full" />
          </div>
        </div>

        {/* 右侧操作区域 */}
        <div className="shrink-0 flex items-center gap-2">
          {/* 删除按钮骨架屏 */}
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </div>
    </Card>
  );
};

export default KnowledgeBaseSkeleton;
