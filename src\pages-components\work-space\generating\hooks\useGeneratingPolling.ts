import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef } from "react";
import { usePollingTask as createPollingTask } from "@/hooks/usePollingTask";
import useGenerateStore, { ProcessingStage } from "@/store/generate";
import useRoleStore from "@/store/persona";

/**
 * 生成轮询逻辑的自定义Hook
 *
 * 管理生成任务的轮询、状态监听和页面跳转
 */
export const useGeneratingPolling = (connectionId: string) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { currentRole } = useRoleStore();
  const {
    generateResult,
    getGenerateResult,
    taskId,
    isFinished,
    setTaskId,
    setSteps,
    setThinkingDataToInit,
  } = useGenerateStore();
  const timerRef = useRef<ReturnType<typeof createPollingTask> | null>(null);
  const initializedRef = useRef(false); // 添加初始化标记

  // 轮询任务函数
  const pollingTask = (taskId: string) => {
    if (!timerRef.current) {
      return;
    }
    timerRef.current.start({
      task: () => getGenerateResult(taskId),
      interval: 2000,
      timeout: 300000,
      immediate: true,
    });
  };

  // 监听生成完成状态
  useEffect(() => {
    if (isFinished && taskId) {
      timerRef.current?.stop();
    }
  }, [isFinished, router, taskId]);

  // 监听taskId变化，开始轮询
  useEffect(() => {
    if (taskId) {
      router.push("/workspace/generating?taskId=" + taskId);
      timerRef.current?.stop();
      pollingTask(taskId);
    }
  }, [taskId]);
  useEffect(() => {
    setTaskId("");
    setSteps(ProcessingStage.INIT);
    setThinkingDataToInit();

    timerRef.current = createPollingTask();
    // 清理函数
    return () => {
      timerRef.current?.stop();
      setTaskId("");
      timerRef.current = null;
      setSteps(ProcessingStage.INIT);
      setThinkingDataToInit();
    };
  }, []);

  // 初始化轮询器并开始生成任务
  useEffect(() => {
    // 避免重复初始化
    if (initializedRef.current) {
      return;
    }

    const urlTaskId = searchParams.get("taskId");
    if (urlTaskId) {
      setTaskId(urlTaskId);
      pollingTask(urlTaskId);
      initializedRef.current = true;
    } else {
      if (
        currentRole &&
        currentRole.personaId &&
        currentRole.personaId.trim() !== "" &&
        connectionId
      ) {
        generateResult(currentRole.personaId, connectionId);
        initializedRef.current = true;
      }
    }
  }, [currentRole?.personaId, connectionId]); // 移除 searchParams.get("taskId") 避免循环依赖

  return {
    isFinished,
    taskId,
  };
};
