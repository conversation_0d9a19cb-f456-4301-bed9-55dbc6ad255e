import React from "react";
import { useLanguage } from "@/hooks/useLanguage";
import type { UniversalUploadSystemProps } from "./types";
import UniversalUploadSystem from "./index";
interface DocumentUploadProps
  extends Omit<UniversalUploadSystemProps, "config"> {
  maxSize?: number; // MB
  multiple?: boolean;
  maxFiles?: number; // 最大文件数量
  acceptTypes?: string[]; // 允许的文档类型
  disabled?: boolean; // 禁用上传功能
  disabledMessage?: React.ReactNode | string; // 禁用时显示的消息
}

/**
 * 专用文档上传组件
 * 只允许上传文档文件
 */
export const DocumentUpload = ({
  maxSize = 50,
  multiple = true,
  maxFiles = 5,
  acceptTypes = ["application/pdf"],
  disabled = true,
  disabledMessage,
  ...props
}: DocumentUploadProps) => {
  const { t } = useLanguage();
  // 检查全局配置和组件级配置
  const finalDisabledMessage = disabledMessage || t("upload:disabled.document");

  // 如果禁用，显示禁用状态
  if (disabled) {
    return (
      <div className="text-text-primary p-6">
        <div className="border-2 border-dashed border-border rounded-lg p-12 text-center bg-surface-light">
          <div className="text-text-secondary mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4 opacity-50"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <div className="text-xl font-medium mb-2 text-text-secondary">
            {finalDisabledMessage}
          </div>
        </div>
      </div>
    );
  }

  const config = {
    mode: "document" as const,
    accept: acceptTypes ? { document: acceptTypes } : undefined,
    maxSize: {
      document: maxSize * 1024 * 1024, // 转换为字节
    },
    multiple,
    maxFiles,
    showPreview: false, // 文档通常不需要预览
    showProgress: true,
  };

  return <UniversalUploadSystem {...props} config={config} />;
};

export default DocumentUpload;
