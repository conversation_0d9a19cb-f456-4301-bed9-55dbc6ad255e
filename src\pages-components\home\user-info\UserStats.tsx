"use client";

import { Card } from "@/components/business/card";
import StatItem from "./StatItem";

interface UserStatsProps {
  stats: Array<{
    value: number;
    label: string;
  }>;
}

const UserStats = ({ stats }: UserStatsProps) => (
  <Card className="bg-surface-light">
    <ul className="grid grid-cols-3 gap-2">
      {stats?.map((stat, index) => (
        <StatItem key={index} value={stat.value} label={stat.label} />
      ))}
    </ul>
  </Card>
);

export default UserStats;
