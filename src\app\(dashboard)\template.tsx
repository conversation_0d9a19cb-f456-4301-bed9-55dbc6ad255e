"use client";

import { motion } from "framer-motion";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { RouteErrorBoundary } from "@/components/RouteErrorBoundary";
import { validateTokenApi } from "@/services/api/user";

export default function Template({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const [isValidating, setIsValidating] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const validateAuth = async () => {
      try {
        // 如果 NextAuth session 存在且有效，直接通过
        if (status === "authenticated" && session?.smartiesToken) {
          setIsAuthenticated(true);
          setIsValidating(false);
          return;
        }

        // 如果 NextAuth 还在加载中，等待
        if (status === "loading") {
          return;
        }

        // 检查本地 token（作为备用验证）
        const localToken = localStorage.getItem("token");
        if (localToken) {
          const res = await validateTokenApi();
          if (res.success) {
            setIsAuthenticated(true);
          } else {
            // Token 无效，清除并重定向
            localStorage.clear();
            window.location.href = "/login";
          }
        } else {
          // 没有任何 token，重定向到登录页
          window.location.href = "/login";
        }
      } catch (error) {
        console.error("Authentication validation failed:", error);
        localStorage.clear();
        window.location.href = "/login";
      } finally {
        setIsValidating(false);
      }
    };

    validateAuth();
  }, [session, status]);

  // 显示加载状态
  if (isValidating || status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 如果未认证，显示空白（即将重定向）
  if (!isAuthenticated) {
    return null;
  }
  return (
    <RouteErrorBoundary>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="h-full"
      >
        {children}
      </motion.div>
    </RouteErrorBoundary>
  );
}
