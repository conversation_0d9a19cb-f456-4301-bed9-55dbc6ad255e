"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Card } from "@/components/business/card";
import { FileData } from "@/components/business/upload/types";
import { Text, TextVariant } from "@/components/ui/Text";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useLanguage } from "@/hooks/useLanguage";
import { usePollingTask as createPollingTask } from "@/hooks/usePollingTask";
import useRoleStore from "@/store/persona";
import usePhotoStore from "@/store/photo";
import { useAppStore } from "@/store/useAppStore";
import CameraRollSkeleton, {
  PhotoGridSkeleton,
} from "./components/CameraRollSkeleton";
import PhotoGrid from "./components/PhotoGrid";
import PhotoHeader from "./components/PhotoHeader";
import ModalUpload from "./ModalUpload";
const PhotoAnalyzer = () => {
  const { t } = useLanguage();
  const { setHeaderSlot } = useAppStore();
  const [open, setOpen] = useState(false);
  const { roleList, currentRole } = useRoleStore();
  const {
    uploadPhoto,
    getPhotoList,
    photoList,
    statistics,
    currentType,
    setCurrentType,
    photoFilter,
    isLoading,
    isFiltering,
    isPolling,
  } = usePhotoStore();
  const timerRef = useRef<ReturnType<typeof createPollingTask> | null>(null);

  const handleUpload = useCallback(() => {
    if (roleList.length === 0) {
      toast.error(t("components:empty.description.noRoles"));
      return;
    }
    setOpen(true);
  }, [roleList.length, t]);

  useEffect(() => {
    setHeaderSlot(<PhotoHeader onUpload={handleUpload} />);
    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot, t, handleUpload]);

  const onUploadComplete = async (files: FileData[]) => {
    const photo = files.map((file) => ({
      fileName: file.name as string,
      s3Key: file.fileKey as string,
    }));
    await uploadPhoto(currentRole.personaId, photo);
    // 移除关闭弹窗的逻辑，现在由 ModalUpload 组件处理
  };

  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getPhotoList(currentRole.personaId);
    }
  }, [currentRole, getPhotoList]);
  useEffect(() => {
    timerRef.current = createPollingTask();
    // 清理函数
    return () => {
      timerRef.current?.stop();
      timerRef.current = null;
    };
  }, []);
  useEffect(() => {
    if (isPolling && timerRef.current) {
      timerRef.current.start({
        task: () => getPhotoList(currentRole.personaId, true),
        interval: 2000,
        timeout: 300000,
        immediate: true,
      });
    } else {
      timerRef.current?.stop();
    }
  }, [isPolling, currentRole]);
  // 显示骨架屏的条件：初始加载或者正在筛选
  if (isLoading || (isFiltering && photoList.length === 0)) {
    return <CameraRollSkeleton />;
  }

  return (
    <div className="grid gap-4">
      <Card>
        <Text
          variant={TextVariant.H4}
          className="mb-4 flex items-center justify-between"
        >
          {t("camera:photoAnalyzer.title")}
          <span className="text-[13px] text-text-tertiary">
            {t("camera:photoAnalyzer.tip")}
          </span>
        </Text>
        <ToggleGroup
          type="single"
          className="flex flex-wrap gap-4 bg-background"
          value={currentType}
          onValueChange={async (value) => {
            await photoFilter(currentRole.personaId, value);
            setCurrentType(value);
          }}
        >
          {statistics.map((item) => (
            <ToggleGroupItem
              key={item.type}
              value={item.type}
              aria-label="Toggle bold"
              className="h-8 data-[state=on]:bg-primary rounded-md flex-none w-auto data-[state=on]:hover:text-text-primary"
            >
              {item.key
                ? t(`camera:photoAnalyzer.statistics.${item.key}`)
                : item.type}
              <span>({item.count})</span>
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
      </Card>
      {isFiltering ? (
        <PhotoGridSkeleton photoCount={6} />
      ) : (
        <PhotoGrid photos={photoList} onUpload={handleUpload} />
      )}
      <ModalUpload
        open={open}
        handleComplete={onUploadComplete}
        close={() => {
          setOpen(false);
        }}
      />
    </div>
  );
};

export default PhotoAnalyzer;
