@tailwind base;
@tailwind components;
@tailwind utilities;

/* 字体加载错误处理 */
@font-face {
  font-family: "Geist-Fallback";
  src: local("system-ui"), local("-apple-system"), local("BlinkMacSystemFont");
  font-display: swap;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  &:focus {
    outline: none;
  }
}
@layer base {
  :root {
    /* ===== 主色调系统 - 从 styles.css 提取 ===== */
    --primary: #ff2e4d;
    --primary-dark: #e61e3d;
    --primary-light: #ff5470;
    --secondary: #ffd700;
    --accent: #00d5ff;

    /* ===== 背景色系统 - 从 styles.css 提取 ===== */
    --background: #0a0a0a;
    --surface: #111111;
    --surface-light: #1a1a1a;
    --surface-lighter: #242424;
    --border: #2a2a2a;
    --border-light: #3a3a3a;

    /* ===== 文本色系统 - 从 styles.css 提取 ===== */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-tertiary: #808080;

    /* ===== 状态色系统 - 从 styles.css 提取 ===== */
    --success: #22c55e;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* ===== 从 styles.css 提取的额外色值 ===== */
    /* 透明度色值 */
    --primary-alpha-05: rgba(255, 46, 77, 0.05);
    --primary-alpha-10: rgba(255, 46, 77, 0.1);
    --primary-alpha-20: rgba(255, 46, 77, 0.2);
    --primary-alpha-30: rgba(255, 46, 77, 0.3);
    --success-alpha-10: rgba(34, 197, 94, 0.1);
    --success-alpha-05: rgba(34, 197, 94, 0.05);
    --warning-alpha-10: rgba(245, 158, 11, 0.1);
    --error-alpha-10: rgba(239, 68, 68, 0.1);
    --info-alpha-10: rgba(59, 130, 246, 0.1);
    --white-alpha-05: rgba(255, 255, 255, 0.05);
    --white-alpha-10: rgba(255, 255, 255, 0.1);
    --white-alpha-20: rgba(255, 255, 255, 0.2);
    --white-alpha-30: rgba(255, 255, 255, 0.3);
    --black-alpha-80: rgba(0, 0, 0, 0.8);

    /* 特殊色值 */
    --brown-color: #8b4513;
    --brown-alpha-10: rgba(139, 69, 19, 0.1);
    --header-backdrop: rgba(10, 10, 10, 0.9);

    /* ===== 渐变系统 ===== */
    --gradient-1: linear-gradient(
      135deg,
      var(--primary) 0%,
      var(--secondary) 100%
    );
    --gradient-2: linear-gradient(
      135deg,
      var(--primary) 0%,
      var(--accent) 100%
    );
    --gradient-primary: linear-gradient(
      135deg,
      var(--primary) 0%,
      var(--secondary) 100%
    );
    --gradient-primary-light: linear-gradient(
      135deg,
      var(--primary) 0%,
      var(--primary-light) 100%
    );
    --gradient-primary-dark: linear-gradient(
      135deg,
      var(--primary-dark) 0%,
      var(--primary) 100%
    );

    /* ===== 阴影系统 - 从 styles.css 提取 ===== */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.6);
    --shadow-glow: 0 0 20px rgba(255, 46, 77, 0.3);
    --shadow-glow-lg: 0 0 40px rgba(255, 46, 77, 0.2);

    /* ===== 玻璃态效果 ===== */
    --glass-bg: rgba(255, 255, 255, 0.03);
    --glass-border: 1px solid rgba(255, 255, 255, 0.1);

    /* ===== 兼容性变量 ===== */
    --foreground: var(--text-primary);

    /* ===== UI 库适配变量 - 映射到项目变量 ===== */
    /* 基础 UI 变量 */
    --card: var(--surface);
    --card-foreground: var(--text-primary);
    --popover: var(--surface-light);
    --popover-foreground: var(--text-primary);
    --muted: var(--surface-lighter);
    --muted-foreground: var(--text-secondary);
    --destructive: var(--error);
    --input: var(--border);
    --ring: var(--primary);

    /* Sidebar 变量 - 映射到项目变量 */
    --sidebar: var(--surface);
    --sidebar-foreground: var(--text-primary);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--text-primary);
    --sidebar-accent: var(--surface-light);
    --sidebar-accent-foreground: var(--text-primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--primary);

    /* 其他 UI 变量 */
    --selection: var(--primary);
    --selection-foreground: var(--text-primary);
    --code: var(--surface-light);
    --code-foreground: var(--text-primary);
    --code-highlight: var(--surface-lighter);
    --code-number: var(--text-secondary);

    /* 圆角变量 */
    --radius: 0.625rem;
  }
}

/* 浅色主题 */
[data-theme="light"] {
  --background: #ffffff;
  --surface: #f8f9fa;
  --surface-light: #ffffff;
  --surface-lighter: #f1f3f4;
  --border: #e5e7eb;
  --border-light: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --foreground: var(--text-primary);

  /* 浅色模式透明度色值 */
  --white-alpha-05: rgba(0, 0, 0, 0.05);
  --white-alpha-10: rgba(0, 0, 0, 0.1);
  --white-alpha-20: rgba(0, 0, 0, 0.2);
  --black-alpha-80: rgba(255, 255, 255, 0.9);
  --header-backdrop: rgba(255, 255, 255, 0.9);

  /* UI 库变量适配 - 浅色主题 */
  --card: var(--surface);
  --card-foreground: var(--text-primary);
  --popover: var(--surface-light);
  --popover-foreground: var(--text-primary);
  --muted: var(--surface-lighter);
  --muted-foreground: var(--text-secondary);
  --destructive: var(--error);
  --input: var(--border);
  --ring: var(--primary);
  --sidebar: var(--surface);
  --sidebar-foreground: var(--text-primary);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--text-primary);
  --sidebar-accent: var(--surface-light);
  --sidebar-accent-foreground: var(--text-primary);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--primary);

  /* 浅色模式阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(255, 46, 77, 0.15);
  --shadow-glow-lg: 0 0 40px rgba(255, 46, 77, 0.1);

  /* 浅色模式玻璃态效果 */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 深色主题 - 基于 styles.css */
[data-theme="dark"] {
  --background: #0a0a0a;
  --surface: #111111;
  --surface-light: #1a1a1a;
  --surface-lighter: #242424;
  --border: #2a2a2a;
  --border-light: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #808080;
  --foreground: var(--text-primary);

  /* 深色模式透明度色值 */
  --white-alpha-05: rgba(255, 255, 255, 0.05);
  --white-alpha-10: rgba(255, 255, 255, 0.1);
  --white-alpha-20: rgba(255, 255, 255, 0.2);
  --black-alpha-80: rgba(0, 0, 0, 0.8);
  --header-backdrop: rgba(10, 10, 10, 0.9);

  /* UI 库变量适配 - 深色主题 */
  --card: var(--surface);
  --card-foreground: var(--text-primary);
  --popover: var(--surface-light);
  --popover-foreground: var(--text-primary);
  --muted: var(--surface-lighter);
  --muted-foreground: var(--text-secondary);
  --destructive: var(--error);
  --input: var(--border);
  --ring: var(--primary);
  --sidebar: var(--surface);
  --sidebar-foreground: var(--text-primary);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--text-primary);
  --sidebar-accent: var(--surface-light);
  --sidebar-accent-foreground: var(--text-primary);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--primary);

  /* 深色模式阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(255, 46, 77, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(255, 46, 77, 0.2);

  /* 深色模式玻璃态效果 */
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 系统偏好设置回退 */
@media (prefers-color-scheme: light) {
  :root:not([data-theme]) {
    --background: #ffffff;
    --surface: #f8f9fa;
    --surface-light: #ffffff;
    --border: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --foreground: var(--text-primary);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: 1px solid rgba(0, 0, 0, 0.1);

    /* UI 库变量适配 - 系统偏好回退 */
    --card: var(--surface);
    --card-foreground: var(--text-primary);
    --popover: var(--surface-light);
    --popover-foreground: var(--text-primary);
    --muted: var(--surface-lighter);
    --muted-foreground: var(--text-secondary);
    --destructive: var(--error);
    --input: var(--border);
    --ring: var(--primary);
    --sidebar: var(--surface);
    --sidebar-foreground: var(--text-primary);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--text-primary);
    --sidebar-accent: var(--surface-light);
    --sidebar-accent-foreground: var(--text-primary);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--primary);
  }
}

/* ===== 页面加载进度条样式 ===== */
#nprogress {
  pointer-events: none !important;
}

#nprogress .bar {
  background: #ff2e4d !important;
  position: fixed !important;
  z-index: 99999 !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 2px !important;
  box-shadow: 0 0 15px #ff2e4d, 0 0 8px #ff2e4d !important;
  border-radius: 0 0 2px 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 15px #ff2e4d, 0 0 8px #ff2e4d;
  opacity: 1;
  transform: rotate(3deg) translate(0px, -4px);
}

/* 进度条动画优化 */
#nprogress .bar {
  transition: width 0.3s ease;
}

/* 深色主题下的进度条 */
[data-theme="dark"] #nprogress .bar {
  box-shadow: 0 0 10px var(--primary), 0 0 5px var(--primary);
}

/* 浅色主题下的进度条 */
[data-theme="light"] #nprogress .bar {
  box-shadow: 0 0 8px var(--primary), 0 0 3px var(--primary);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont,
    "Segoe UI", "Inter", Roboto, system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* 动画背景 */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 50%,
      rgba(99, 102, 241, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(20, 184, 166, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 20%,
      rgba(245, 158, 11, 0.05) 0%,
      transparent 50%
    );
  z-index: -1;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(-20px, -20px) rotate(1deg);
  }
  66% {
    transform: translate(20px, -10px) rotate(-1deg);
  }
}

/* 工具类 */
.glass-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.loading-pulse {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--primary);
  border-radius: 50%;
  animation: pulse-dot 1.4s ease-in-out infinite;
}

@keyframes pulse-dot {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 骨架屏加载动画 */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--surface) 25%,
    var(--surface-light) 50%,
    var(--surface) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s ease-in-out infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.active-nav {
  &::before {
    content: "";
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 24px;
    background: var(--primary);
    border-radius: 0px 2px 2px 0px;
  }
}
.persona-introduction {
  position: relative;
  overflow: hidden;
}
.persona-introduction::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.app-logo {
  padding: 7.5px;
  border-bottom: 1px solid var(--border);
  background: var(--surface);
}

.app-logo .logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-logo .logo-container:hover {
  transform: scale(1.05);
}

.app-logo .persona-icon {
  width: 40px;
  height: 40px;
  position: relative;
}

.app-logo .persona-layers {
  position: relative;
  width: 100%;
  height: 100%;
}

.app-logo .persona-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-logo .layer-1 {
  background: linear-gradient(135deg, #ff2e4d, #ff6b7a);
  transform: rotate(0deg);
  animation: rotate1 8s linear infinite;
}

.app-logo .layer-2 {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  transform: scale(0.85) rotate(120deg);
  animation: rotate2 10s linear infinite reverse;
  opacity: 0.8;
}

.app-logo .layer-3 {
  background: linear-gradient(135deg, #06b6d4, #22d3ee);
  transform: scale(0.7) rotate(240deg);
  animation: rotate3 12s linear infinite;
  opacity: 0.6;
}

@keyframes rotate1 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate2 {
  from {
    transform: scale(0.85) rotate(120deg);
  }
  to {
    transform: scale(0.85) rotate(480deg);
  }
}

@keyframes rotate3 {
  from {
    transform: scale(0.7) rotate(240deg);
  }
  to {
    transform: scale(0.7) rotate(600deg);
  }
}

.app-logo .logo-text {
  font-size: 24px;
  font-weight: 900;
  letter-spacing: -1px;
  background: linear-gradient(135deg, #ffffff 0%, #ff2e4d 50%, #7c3aed 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.spin360 {
  animation: spin360 2s linear infinite;
}

@keyframes spin360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
