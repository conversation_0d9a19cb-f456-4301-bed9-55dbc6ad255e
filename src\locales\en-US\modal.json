{"addRole": {"title": "Add New Identity", "roleName": "Identity Name", "roleNamePlaceholder": "e.g., <PERSON><PERSON>", "gender": "Gender", "genderPlaceholder": "Select Gender", "male": "Male", "female": "Female", "mbtiType": "MBTI Type", "mbtiPlaceholder": "Select MBTI Type", "mbtiOptions": {"ENFP": "ENFP - The Campaigner", "INFP": "INFP - The Mediator", "ENFJ": "ENFJ - The Protagonist", "INFJ": "INFJ - The Advocate", "ENTP": "ENTP - The Debater", "INTP": "INTP - The Logician", "ENTJ": "ENTJ - The Commander", "INTJ": "INTJ - The Architect", "ESFP": "ESFP - The Entertainer", "ISFP": "ISFP - The Adventurer", "ESFJ": "ESFJ - The Consul", "ISFJ": "ISFJ - The Protector", "ESTP": "ESTP - The Entrepreneur", "ISTP": "ISTP - The Virtuoso", "ESTJ": "ESTJ - The Executive", "ISTJ": "ISTJ - The Logistician"}, "avatar": "Avatar Expression", "avatarPlaceholder": "Choose an emoji, e.g., 🌸", "personality": "Personality Traits", "personalityPlaceholder": "e.g., Lively, curious, and passionate about life", "roleIntro": "Role Introduction", "roleIntroPlaceholder": "Detailed description of the character's background, interests, creative tendencies, etc...", "topicPreference": "Expert Topics", "topicPlaceholder": "Separated by commas, e.g., Daily life, Food, Travel, Mood sharing", "validation": {"nameRequired": "Please enter a character name", "nameMinLength": "Character name must be at least 2 characters", "nameMaxLength": "Character name cannot exceed 20 characters", "genderRequired": "Please select a gender", "mbtiRequired": "Please select an MBTI type", "personalityRequired": "Please enter personality traits", "personalityMinLength": "Personality description must be at least 5 characters", "introductionRequired": "Please enter a detailed introduction", "introductionMinLength": "Detailed introduction must be at least 10 characters"}}, "addSource": {"title": "Add Custom Information Source", "sourceName": "Source Name", "sourceNamePlaceholder": "e.g., TechCrunch", "sourceType": "Source Type", "sourceTypePlaceholder": "Select source type", "rssType": "RSS Feed", "urlAddress": "URL Address", "websiteType": "Website URL", "apiType": "Social Media Account", "urlPlaceholder": "https://example.com/feed", "contentType": "Content Category", "techNews": "Tech News", "lifestyle": "Lifestyle", "fashion": "Fashion & Beauty", "food": "Food & Travel", "validation": {"sourceNameRequired": "Please enter a source name", "sourceNameMinLength": "Source name must be at least 2 characters", "sourceTypeRequired": "Please select a source type", "urlRequired": "Please enter a URL address", "urlInvalid": "Please enter a valid URL address"}}, "imageModal": {"title": "Image Preview", "toolbar": {"zoomOut": "Zoom Out", "zoomIn": "Zoom In", "rotateLeft": "Rotate Left", "rotateRight": "Rotate Right", "reset": "Reset", "fullscreen": "Fullscreen", "download": "Download", "close": "Close"}, "shortcuts": {"title": "Shortcuts", "close": "Close", "zoom": "Zoom", "rotate": "Rotate", "reset": "Reset", "drag": "Drag", "move": "Move", "wheel": "Wheel"}, "info": {"scale": "Scale", "rotation": "Rotation"}}, "publishToPlatform": {"title": "Publish to Platform", "connectWithTwitter": "Connect with X (Twitter):", "twitterX": "Twitter/X", "connectAccount": "Connect Account", "cancel": "Cancel", "connectWithX": "Connect with X"}, "connectToTwitter": {"title": "Connect to X (Twitter)", "permissionTitle": "To publish posts automatically, we need permission to post on your behalf.", "whatWeAccess": "What we'll access:", "postTweets": "Post tweets on your behalf", "readProfile": "Read your profile information", "notReadDMs": "We will NOT read your DMs", "notAccessFollowers": "We will NOT access your followers", "dataSecure": "Your data is encrypted and secure. You can revoke access anytime in Settings.", "authorize": "Authorize", "connecting": "Connecting...", "notConnect": "Not Connected"}, "publishToTwitter": {"title": "Publish to Platform", "publishTo": "Publish to X (Twitter):", "connectedAccount": "Connected Account:", "followers": "followers", "disconnect": "Disconnect", "publishingOptions": "Publishing Options:", "postNow": "Post Now (Once)", "autoPost": "Auto-Post Every", "hours": "hours", "publish": "Publish", "publishing": "Publishing...", "publishSuccess": "Published successfully!"}}