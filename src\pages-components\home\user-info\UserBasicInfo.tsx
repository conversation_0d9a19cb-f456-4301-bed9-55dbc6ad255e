"use client";

import { Card } from "@/components/business/card";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { ProfileIcon } from "@/components/ProfileIcon";
import { Badge } from "@/components/ui/badge";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";

interface UserBasicInfoProps {
  id: string;
  roleName: string;
  gender: string;
  mbtiType: string;
}

const UserBasicInfo = ({
  id,
  roleName,
  gender,
  mbtiType,
}: UserBasicInfoProps) => {
  const { t } = useLanguage();
  if (!id) {
    return (
      <Card>
        <EmptyState
          icon={<ProfileIcon seed="no-roles" size={60}></ProfileIcon>}
          compact
          isDescription={false}
          type={EmptyType.NO_ROLES}
        />
      </Card>
    );
  }

  return (
    <Card className="hover:border-ring bg-surface-light">
      <div className="flex gap-4 items-center">
        <div className="w-14 h-14 border border-border text-2xl rounded-full bg-surface-light flex items-center justify-center">
          <ProfileIcon seed={id} size={60}></ProfileIcon>
        </div>
        <div>
          <Text variant={TextVariant.H4} className="mb-1">
            {roleName}
          </Text>
          <Badge variant="outline" className="border-accent text-accent">
            {t(`home:${gender}`)}
          </Badge>
          <Badge variant="outline" className="border-brown text-brown ml-1">
            {mbtiType}
          </Badge>
        </div>
      </div>
    </Card>
  );
};

export default UserBasicInfo;
