"use client";

import { useState, useEffect, useCallback } from "react";

interface CacheConfig<T> {
  key: string;
  ttl?: number; // Time to live in milliseconds
  initialData?: T;
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class CacheManager {
  private cache = new Map<string, CacheItem<any>>();

  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) {
      return null;
    }

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) {
      return false;
    }

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局缓存实例
const cacheManager = new CacheManager();

// 定期清理过期缓存
if (typeof window !== "undefined") {
  setInterval(() => {
    cacheManager.cleanup();
  }, 60000); // 每分钟清理一次
}

export const useCache = <T>({
  key,
  ttl = 5 * 60 * 1000,
  initialData,
}: CacheConfig<T>) => {
  const [data, setData] = useState<T | null>(() => {
    const cached = cacheManager.get<T>(key);
    return cached || initialData || null;
  });

  const updateCache = useCallback(
    (newData: T) => {
      cacheManager.set(key, newData, ttl);
      setData(newData);
    },
    [key, ttl]
  );

  const clearCache = useCallback(() => {
    cacheManager.delete(key);
    setData(initialData || null);
  }, [key, initialData]);

  const hasCache = useCallback(() => {
    return cacheManager.has(key);
  }, [key]);

  useEffect(() => {
    const cached = cacheManager.get<T>(key);
    if (cached && cached !== data) {
      setData(cached);
    }
  }, [key, data]);

  return {
    data,
    updateCache,
    clearCache,
    hasCache,
    cacheManager: {
      clear: cacheManager.clear.bind(cacheManager),
      cleanup: cacheManager.cleanup.bind(cacheManager),
    },
  };
};
