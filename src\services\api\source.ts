import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";
export enum StatusEnum {
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
  INIT = "INIT",
  PARSING = "PARSING",
}
export interface IFile {
  id: string; // 文件唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  fileName: string; // 文件名称
  fileType: string; // 文件类型
  fileSize: number; // 文件大小
  imgStatus: StatusEnum; // 图片处理状态
  s3Key: string; // S3存储路径
  embeddingStatus: StatusEnum; // 向量处理状态
  totalPage: number; // 总页数
  createdAt: string;
  updatedAt: string;
}

// 上传文件
export const uploadFileApi = async (data: {
  personaId: string;
  files: {
    fileName: string;
    s3Key: string;
    size: number;
    type: string;
  }[];
}): Promise<ApiResponse<{ fileKey: string }>> => {
  return await api.post(`/file/import`, data);
};

// fileList
export const fileListApi = async (data: {
  personaId: string;
}): Promise<
  ApiResponse<{
    pagination: { count: number; hasMore: boolean };
    files: {
      files: IFile[];
    };
  }>
> => {
  return await api.postWithCache(`/file/list`, data);
};
// file/delete
export const deleteFileApi = async (data: {
  fileId: string;
}): Promise<ApiResponse<{ success: boolean }>> => {
  return await api.post(`/file/delete`, data);
};

export interface ISource {
  id?: string; // 信息源ID
  personaId: string; // 人设ID
  type: "rss" | "url" | "account"; // 信息源类型
  name: string; // 信息源名称
  url: string; // 信息源URL
  isBuiltIn?: boolean; // 是否内置信息源
  isDomestic?: boolean; // 国内/国外
  isConnection?: boolean; // 是否已连接
  contentType?: string[]; // 内容类型
}
// source/create
export const createSourceApi = async (
  data: ISource
): Promise<ApiResponse<{ sourceId: string }>> => {
  return await api.post(`/source/create`, data);
};

// source/list
export const sourceListApi = async (
  personaId: string
): Promise<
  ApiResponse<{
    count: number;
    sources: ISource[];
  }>
> => {
  return await api.postWithCache(`/source/list`, { personaId });
};

//source/delete
export const deleteSourceApi = async (data: { sourceId: string }) => {
  return await api.post(`/source/delete`, data);
};
