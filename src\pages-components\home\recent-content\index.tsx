"use client";

import clsx from "clsx";
import { useEffect } from "react";
import { Card } from "@/components/business/card";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import useGenerateStore from "@/store/generate";
import useRoleStore from "@/store/persona";

const RecentContent = () => {
  const { t } = useLanguage();
  const { getGenerateList, historyList } = useGenerateStore();
  const { currentRole } = useRoleStore();
  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getGenerateList(currentRole.personaId);
    }
  }, [currentRole]);

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex justify-between items-center"
      >
        <span>{t("home:recentContent.title")}</span>
        <span className="text-[12px] px-4 py-2 rounded-md cursor-pointer text-text-secondary hover:bg-white-alpha-10">
          {t("button:viewAll")}
        </span>
      </Text>

      <div className="grid gap-4">
        {historyList.length === 0 && <EmptyState type={EmptyType.NO_CONTENT} />}
        {historyList.slice(0, 5).map((item, index) => (
          <Card
            key={index}
            className="bg-surface-light hover:bg-white-alpha-10"
          >
            <div className="flex gap-4 items-center">
              <div>
                <Text variant={TextVariant.BODY_SMALL}>
                  {item.title || "无标题"}
                </Text>
                <Text
                  variant={TextVariant.CAPTION}
                  className="text-text-tertiary"
                >
                  {new Date(item.createdAt as string).toLocaleDateString()}
                </Text>
              </div>
              <div className="flex-1 flex justify-end">
                <Text
                  variant={TextVariant.CAPTION}
                  className={clsx(
                    item.status === "PUBLISHED"
                      ? "text-primary"
                      : "text-text-tertiary"
                  )}
                >
                  {item.status === "PUBLISHED"
                    ? t("button:published")
                    : t("button:unpublish")}
                </Text>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </Card>
  );
};

export default RecentContent;
