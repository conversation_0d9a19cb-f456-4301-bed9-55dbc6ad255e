"use client";

import { Badge } from "@/components/ui/badge";

interface TopicTagsProps {
  topics: string[];
}

const TopicTags = ({ topics }: TopicTagsProps) => (
  <div className="flex gap-2 flex-wrap">
    {topics.map((topic, index) => (
      <Badge
        key={index}
        variant="secondary"
        className="py-2 px-4 rounded-full hover:border-ring hover:text-primary"
      >
        {topic}
      </Badge>
    ))}
  </div>
);

export default TopicTags;
