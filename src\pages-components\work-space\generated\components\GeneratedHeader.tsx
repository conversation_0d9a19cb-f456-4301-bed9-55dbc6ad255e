"use client";

import { ArrowLeftCircleIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import { GeneratedHeaderProps } from "../types";

/**
 * 生成结果页面的头部操作按钮组件
 * 包含重新生成和返回工作台的按钮
 */
const GeneratedHeader = ({ router }: GeneratedHeaderProps) => {
  const { t } = useLanguage();

  return (
    <div className="flex gap-2 items-center">
      <Button size="sm" onClick={() => router.replace("/workspace/generating")}>
        {t("button:regenerate")}
      </Button>
      <Button
        variant="secondary"
        size="sm"
        onClick={() => router.replace("/workspace")}
      >
        <ArrowLeftCircleIcon className="size-4" />
        {t("button:backToWorkspace")}
      </Button>
    </div>
  );
};

export default GeneratedHeader;
