import { DialogModal } from "@/components/business/modal";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";
import useSourceStore from "@/store/source";
import SourceForm, { SourceFormData } from "./SourceForm";

const ModalAddSource = ({
  open,
  close,
}: {
  open: boolean;
  close: () => void;
}) => {
  const { createSource, isCreating } = useSourceStore();
  const { t } = useLanguage();
  const { currentRole } = useRoleStore();
  const handleCreate = async (data: SourceFormData) => {
    const { urlAddress, sourceName, sourceType, contentTypes } = data;
    close();
    setTimeout(async () => {
      await createSource({
        personaId: currentRole.personaId,
        type: sourceType,
        name: sourceName,
        url: urlAddress,
        contentType: contentTypes,
        isBuiltIn: false,
        isDomestic: false,
        isConnection: true,
      });
    }, 0);
  };

  return (
    <DialogModal
      open={open}
      openChange={close}
      className="w-[36rem] bg-surface"
      title={t("modal:addSource.title")}
    >
      <SourceForm
        onSubmit={handleCreate}
        onCancel={close}
        isLoading={isCreating}
      />
    </DialogModal>
  );
};

export default ModalAddSource;
