"use client";

import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Card } from "@/components/business/card";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { Button } from "@/components/ui/button";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import useRoleStore from "@/store/persona";
import useSourceStore from "@/store/source";
import CustomInformationSourceSkeleton from "./CustomInformationSourceSkeleton";
import ModalAddSource from "./ModalAddSource";

const CustomInformationSource = () => {
  const { roleList, currentRole } = useRoleStore();
  const { t } = useLanguage();
  const {
    getSourceList,
    sourceList,
    deleteSource,
    isLoading,
    deletingSourceId,
  } = useSourceStore();
  const handleOpenModal = () => {
    if (roleList.length === 0) {
      toast.error(t("components:empty.description.noRoles"));
      return;
    }
    setModalOpen(true);
  };

  // 初始数据
  const [modalOpen, setModalOpen] = useState(false);
  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getSourceList(currentRole.personaId);
    }
  }, [currentRole, getSourceList]);

  // 显示骨架屏的条件：正在加载且没有数据
  if (isLoading && sourceList.length === 0) {
    return <CustomInformationSourceSkeleton sourceCount={3} />;
  }

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("information:customInformationSource.title")}
        <div className="flex items-center gap-2">
          <span className="text-[13px] text-text-tertiary">
            {t("information:customInformationSource.tip")}
          </span>
          <Button
            size="sm"
            onClick={() => {
              handleOpenModal();
            }}
            className="flex items-center gap-1"
          >
            <PlusIcon className="size-4" />
            {t("button:addSource")}
          </Button>
        </div>
      </Text>

      <div className="grid gap-4">
        {sourceList.map((item) => (
          <Card key={item.id} className="bg-surface-light">
            <div className="flex justify-between items-center">
              <div className="flex-1 min-w-0">
                <Text variant={TextVariant.BODY_MEDIUM}>{item.name}</Text>
                <Text
                  variant={TextVariant.CAPTION}
                  className="text-text-tertiary truncate"
                >
                  {item.url}
                </Text>
              </div>
              <Button
                variant="outline"
                size="sm"
                disabled={deletingSourceId === item.id}
                onClick={() =>
                  deleteSource(item.id as string, currentRole.personaId)
                }
                className="text-error hover:bg-error/10 hover:border-error/30 ml-4 shrink-0"
              >
                <TrashIcon className="size-4" />
                {deletingSourceId === item.id
                  ? t("button:loading")
                  : t("button:remove")}
              </Button>
            </div>
          </Card>
        ))}

        {sourceList.length === 0 && (
          <div className="text-center py-8">
            <EmptyState
              type={EmptyType.NO_CONTENT}
              actions={[
                {
                  label: t("button:addSource"),
                  icon: <PlusIcon className="size-4"></PlusIcon>,
                  onClick: () => {
                    handleOpenModal();
                  },
                  variant: "default",
                },
              ]}
            ></EmptyState>
          </div>
        )}
      </div>
      <ModalAddSource open={modalOpen} close={() => setModalOpen(false)} />
    </Card>
  );
};

export default CustomInformationSource;
