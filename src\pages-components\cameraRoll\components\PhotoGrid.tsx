"use client";

import { ArrowUpTrayIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { useLanguage } from "@/hooks/useLanguage";
import { IPhoto } from "@/services/api/photo";
import useRoleStore from "@/store/persona";
import PhotoCard from "./PhotoCard";

interface PhotoGridProps {
  photos: IPhoto[];
  onUpload: () => void;
}

const PhotoGrid = ({ photos, onUpload }: PhotoGridProps) => {
  const { t } = useLanguage();
  const { roleList } = useRoleStore();

  return (
    <div
      className={clsx(
        "grid gap-4",
        photos.length === 0
          ? "grid-cols-1"
          : "grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5"
      )}
    >
      {photos.length === 0 && (
        <EmptyState
          className="h-[calc(100vh-300px)]"
          type={EmptyType.NO_IMAGES}
          actions={
            roleList.length !== 0
              ? []
              : [
                  {
                    label: t("camera:photoAnalyzer.uploadPhoto"),
                    icon: <ArrowUpTrayIcon className="size-4" />,
                    onClick: onUpload,
                    variant: "secondary",
                  },
                ]
          }
        />
      )}
      {photos.map((photo) => (
        <PhotoCard key={photo.id} photo={photo} />
      ))}
    </div>
  );
};

export default PhotoGrid;
