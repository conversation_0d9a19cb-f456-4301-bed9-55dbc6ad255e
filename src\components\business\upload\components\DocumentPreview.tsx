import clsx from "clsx";
import { Trash2, Loader2 } from "lucide-react";
import type { FilePreviewProps } from "../types";
import { formatFileSize } from "../utils/fileUtils";
import { ErrorMessage } from "./ErrorMessage";
import { FileIcon } from "./FileIcon";
import { ProgressBar } from "./ProgressBar";
import { StatusBadge } from "./StatusBadge";
import { StatusIcon } from "./StatusIcon";

export const DocumentPreview = ({
  file,
  onRemove,
  isShowBar = true,
  className,
  isDeleting = false,
}: FilePreviewProps) => {
  return (
    <div
      className={clsx(
        "bg-surface rounded-lg p-4 border border-border",
        className
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1">
          <FileIcon file={file.file} size="w-8 h-8" />

          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="font-medium text-text-primary truncate">
                {file.name}
              </h3>
              <StatusIcon status={file.status} />
            </div>
            <div className="flex items-center space-x-4 mt-1 text-sm text-text-secondary">
              <span>{formatFileSize(file.size)}</span>
              <span>{file.uploadTime}</span>
              <StatusBadge status={file.status} />
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* {file.status === "success" && (
            <button className="p-2 text-text-secondary hover:text-text-primary transition-colors">
              <Download className="w-4 h-4" />
            </button>
          )} */}
          <button
            onClick={() => onRemove(file.id)}
            disabled={isDeleting}
            className="p-2 text-text-secondary hover:text-error transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeleting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* 显示进度条：上传中、成功、失败时都显示 */}
      {(file.status === "uploading" ||
        file.status === "success" ||
        file.status === "error") &&
        isShowBar && <ProgressBar fileId={file.id} file={file} />}

      {file.errors.length > 0 && <ErrorMessage errors={file.errors} />}
    </div>
  );
};
