/**
 * 语言配置文件
 *
 * 这个文件用于配置应用的初始语言设置
 * 可以通过修改这里的配置来改变应用的默认语言行为
 */

// 应用初始语言配置
export const INITIAL_LANGUAGE_CONFIG = {
  /**
   * 应用初始语言
   *
   * 优先级：
   * 1. 环境变量 NEXT_PUBLIC_DEFAULT_LANGUAGE
   * 2. 这里设置的 FALLBACK_INITIAL_LANGUAGE
   * 3. 用户本地存储的语言偏好
   * 4. 浏览器语言检测
   *
   * 支持的语言：
   * - "zh-CN": 简体中文
   * - "en-US": 英语（美国）
   * - "ja-JP": 日语
   */
  FALLBACK_INITIAL_LANGUAGE: "zh-CN",

  /**
   * 是否强制使用初始语言
   *
   * true: 忽略用户的本地存储和浏览器语言，始终使用初始语言
   * false: 遵循 i18next 的语言检测顺序（推荐）
   */
  FORCE_INITIAL_LANGUAGE: false,

  /**
   * 语言检测顺序
   *
   * 可选值：
   * - "localStorage": 本地存储
   * - "navigator": 浏览器语言
   * - "htmlTag": HTML lang 属性
   * - "cookie": Cookie
   * - "querystring": URL 查询参数
   * - "path": URL 路径
   * - "subdomain": 子域名
   */
  DETECTION_ORDER: ["localStorage", "navigator", "htmlTag"] as string[],

  /**
   * 是否启用语言自动检测
   *
   * true: 启用自动检测（推荐）
   * false: 禁用自动检测，始终使用初始语言
   */
  ENABLE_AUTO_DETECTION: true,
} as const;

/**
 * 获取应用初始语言
 *
 * 根据配置和环境变量确定应用的初始语言
 */
export function getInitialLanguage() {
  return INITIAL_LANGUAGE_CONFIG.FALLBACK_INITIAL_LANGUAGE;
}

/**
 * 获取语言检测配置
 *
 * 根据配置返回 i18next 的语言检测设置
 */
export function getLanguageDetectionConfig() {
  if (!INITIAL_LANGUAGE_CONFIG.ENABLE_AUTO_DETECTION) {
    return {
      order: [] as string[],
      caches: [] as string[],
    };
  }

  return {
    order: [...INITIAL_LANGUAGE_CONFIG.DETECTION_ORDER],
    caches: ["localStorage"] as string[],
    lookupLocalStorage: "i18nextLng",
  };
}
