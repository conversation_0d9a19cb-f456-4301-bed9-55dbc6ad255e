"use client";

import { Text, TextVariant } from "@/components/ui/Text";

interface StatItemProps {
  value: number;
  label: string;
}

const StatItem = ({ value, label }: StatItemProps) => (
  <li className="text-center grid grid-rows-2 place-items-center">
    <Text variant={TextVariant.BODY_LARGE} className="text-primary">
      {value}
    </Text>
    <Text variant={TextVariant.CAPTION} className="text-text-tertiary">
      {label}
    </Text>
  </li>
);

export default StatItem;
