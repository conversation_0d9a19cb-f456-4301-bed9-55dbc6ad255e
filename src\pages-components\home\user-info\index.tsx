"use client";

import { useEffect, useMemo } from "react";
import { Card } from "@/components/business/card";
import { useLanguage } from "@/hooks/useLanguage";
import useGenerateStore from "@/store/generate";
import useRoleStore from "@/store/persona";
import RoleIntroduction from "./RoleIntroduction";
import UserBasicInfo from "./UserBasicInfo";
import UserStats from "./UserStats";

const UserInfo = () => {
  const { getGenerateStatistics, statistics } = useGenerateStore();
  const { currentRole } = useRoleStore();
  const { t } = useLanguage();

  // 模拟统计数据，实际应该从 store 或 API 获取
  const statsData = useMemo(() => {
    return [
      {
        value: statistics.totalGenerated,
        label: t("home:userInfo.stats.content"),
      },
      {
        value: statistics.published,
        label: t("home:userInfo.stats.published"),
      },
      { value: statistics.favorites, label: t("button:saved") },
    ];
  }, [statistics, t]);
  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getGenerateStatistics(currentRole.personaId);
    }
  }, [currentRole]);

  return (
    <Card className="w-full">
      <div className="grid grid-cols-[240px_1fr] max-md:grid-cols-1 gap-10 px-4 py-2 items-start">
        <div className="grid gap-4">
          <UserBasicInfo
            id={currentRole.personaId}
            roleName={currentRole.name}
            gender={currentRole.gender}
            mbtiType={currentRole.mbti}
          />
          <UserStats stats={statsData} />
        </div>

        <RoleIntroduction
          personality={currentRole.personality || ""}
          description={currentRole.introduction || ""}
          topics={currentRole.topics ? currentRole.topics.split(",") : []}
        />
      </div>
    </Card>
  );
};

export default UserInfo;
