import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";

interface IStartRes {
  fileKey: string;
  uploadId: string;
}

interface IPPresign {
  presignUrl: string;
}

// 开始上传

export const startUploadApi = async (data: {
  fileName: string;
  contentType: string;
}): Promise<ApiResponse<IStartRes>> => {
  return await api.post(`/upload/start`, data);
};

// 获取预签名URL
export const getPresignUrlApi = async (data: {
  fileKey: string;
  uploadId: string;
  partNumber: number;
}): Promise<ApiResponse<IPPresign>> => {
  return await api.post(`/upload/presign`, data);
};

// 完成上传
export const getCompleteApi = async (data: {
  fileKey: string;
  uploadId: string;
  parts: { PartNumber: number; ETag: string }[];
}): Promise<ApiResponse<{ fileUrl?: string; fileKey: string }>> => {
  return await api.post(`/upload/complete`, data);
};
