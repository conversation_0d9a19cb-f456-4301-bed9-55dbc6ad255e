"use client";

import { useEffect } from "react";
import useConfigStore from "@/store/useConfig";

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Theme Provider 组件
 *
 * 负责根据 store 中的主题设置初始化和管理主题
 */
export default function ThemeProvider({ children }: ThemeProviderProps) {
  const { theme, setTheme } = useConfigStore();

  useEffect(() => {
    // 初始化主题
    const initializeTheme = () => {
      // 1. 优先使用 localStorage 中保存的主题
      const savedTheme = (
        typeof window !== "undefined" ? localStorage.getItem("theme") : null
      ) as "light" | "dark" | null;

      // 3. 如果 store 中的主题与初始主题不同，更新 store
      if (theme !== savedTheme) {
        setTheme(savedTheme || "dark");
      } else {
        // 如果主题相同，确保 DOM 属性正确设置
        document.documentElement.setAttribute("data-theme", theme);
      }
    };

    // 只在客户端执行
    if (typeof window !== "undefined") {
      initializeTheme();
    }
  }, []); // 只在组件挂载时执行一次

  useEffect(() => {
    // 当 store 中的主题发生变化时，更新 DOM
    if (typeof window !== "undefined") {
      document.documentElement.setAttribute("data-theme", theme);
      localStorage.setItem("theme", theme);
    }
  }, [theme]);

  return <>{children}</>;
}
