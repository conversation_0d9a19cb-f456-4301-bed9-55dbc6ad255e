import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { aiSettingApi, IConfig, getAiConfigApi } from "@/services/api/setting";
interface PhotoStore {
  aiSetting: IConfig;
  createAiSetting: (data: IConfig) => Promise<void>;
  getAiSetting: (personaId: string) => Promise<void>;
  // updateAiSetting: (data: IConfig) => Promise<void>;
}
const useSettingStore = create<PhotoStore>()(
  immer(
    (set, get): PhotoStore => ({
      aiSetting: {} as IConfig,
      createAiSetting: async (data: IConfig) => {
        const res = await aiSettingApi(data);
        if (res.success) {
          get().getAiSetting(data.personaId as string);
        }
      },
      getAiSetting: async (personaId: string) => {
        const res = await getAiConfigApi(personaId);
        if (res.success) {
          set({
            aiSetting: res.data?.config,
          });
        }
      },
    })
  )
);

export default useSettingStore;
