import { Card } from "@/components/business/card";
import { Skeleton } from "@/components/ui/skeleton";

const UserInfoSkeleton = () => (
  <Card className="w-full">
    <div className="grid grid-cols-[240px_1fr] max-md:grid-cols-1 gap-10 px-4 py-2 items-start">
      {/* 左侧：用户基本信息和统计数据骨架屏 */}
      <div className="grid gap-4">
        {/* UserBasicInfo 骨架屏 */}
        <Card className="bg-surface-light">
          <div className="flex gap-4 items-center">
            {/* 头像骨架屏 */}
            <div className="w-14 h-14 border border-border rounded-full bg-surface-light flex items-center justify-center">
              <Skeleton className="w-12 h-12 rounded-full" />
            </div>
            {/* 用户信息骨架屏 */}
            <div className="flex-1">
              <Skeleton className="h-6 w-24 mb-2" />
              <div className="flex gap-1">
                <Skeleton className="h-5 w-12 rounded-full" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
            </div>
          </div>
        </Card>

        {/* UserStats 骨架屏 */}
        <Card className="bg-surface-light">
          <div className="grid grid-cols-3 gap-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="text-center">
                <Skeleton className="h-6 w-8 mx-auto mb-1" />
                <Skeleton className="h-3 w-12 mx-auto" />
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* 右侧：角色介绍骨架屏 */}
      <Card className="bg-surface-light">
        <div className="grid gap-8">
          {/* 标题骨架屏 */}
          <div className="border-b border-border pb-2 relative">
            <Skeleton className="h-6 w-32" />
            <div className="absolute -bottom-0.5 left-0 bg-ring w-14 h-0.5"></div>
          </div>

          {/* 性格特点骨架屏 */}
          <div className="grid gap-2">
            <Skeleton className="h-5 w-16 mb-2" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
          {/* 性格特点骨架屏 */}
          <div className="grid gap-2">
            <Skeleton className="h-5 w-16 mb-2" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>

          {/* 详细介绍骨架屏 */}
          <div className="grid gap-2">
            <Skeleton className="h-5 w-20 mb-2" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>

          {/* 专业话题骨架屏 */}
          <div className="grid gap-2">
            <Skeleton className="h-5 w-20 mb-2" />
            <div className="flex flex-wrap gap-2">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton key={index} className="h-6 w-16 rounded-full" />
              ))}
            </div>
          </div>
        </div>
      </Card>
    </div>
  </Card>
);

export default UserInfoSkeleton;
