import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  IGenerationTask,
  generateApi,
  generateGet<PERSON>ist<PERSON><PERSON>,
  generateResultApi,
  IGenerateList,
  generateUpdateApi,
  generateStatisticsApi,
  // generateGetGenerateApi,
} from "@/services/api/generate";

export enum ProcessingStage {
  INIT = "INIT", // 初始化
  KEYWORD_GENERATION = "KEYWORD_GENERATION", // 关键词生成
  // PHOTO_ANALYSIS = "PHOTO_ANALYSIS", // 照片分析
  VECTOR_SEARCH = "VECTOR_SEARCH", // 向量搜索
  MCP_FETCH = "MCP_FETCH", // 外部数据获取
  CONTENT_GENERATION = "CONTENT_GENERATION", // 内容生成
  FINAL_GENERATION = "FINAL_GENERATION", // 最终生成
}
interface GenerateStore {
  resultList: IGenerationTask[];
  isGenerating: boolean;
  currentGeneratingPersonaId: string | null;
  generateResult: (personaId: string, connectionId: string) => Promise<void>;
  getGenerateResult: (taskId: string) => Promise<void>;
  taskId: string;
  isFinished: boolean;
  finalContent: IGenerateList[];
  getResultByTaskId: (taskId: string) => Promise<void>;
  getGenerateList: (personaId: string) => Promise<void>;
  historyList: IGenerateList[];
  publish: (
    data: IGenerateList,
    taskId: string,
    personaId: string
  ) => Promise<void>;
  save: (
    data: IGenerateList,
    taskId: string,
    personaId: string
  ) => Promise<void>;
  setTaskId: (taskId: string) => void;
  getGenerateStatistics: (personaId: string) => Promise<void>;
  statistics: {
    totalGenerated: number; // 总共生成的内容条数（模型生成的条数）
    published: number; // 发布条数
    favorites: number; // 保存条数（收藏）
  };
  currentSteps: ProcessingStage;
  thinkingData: string;
  setThinkingData: (data: string) => void;
  setSteps: (steps: ProcessingStage) => void;
  setThinkingDataToInit: () => void;
}
const useGenerateStore = create<GenerateStore>()(
  immer(
    (set, get): GenerateStore => ({
      resultList: [],
      isGenerating: false,
      currentGeneratingPersonaId: null,
      taskId: "",
      isFinished: false,
      finalContent: [],
      historyList: [],
      currentSteps: ProcessingStage.INIT,
      thinkingData: "",
      statistics: {
        totalGenerated: 0, // 总共生成的内容条数（模型生成的条数）
        published: 0, // 发布条数
        favorites: 0, // 保存条数（收藏）
      },
      setSteps: (steps: ProcessingStage) => {
        set({
          currentSteps: steps,
        });
      },
      setThinkingData: (data: string) => {
        set({
          thinkingData: get().thinkingData + data,
        });
      },
      setThinkingDataToInit: () => {
        set({
          thinkingData: "",
        });
      },
      getGenerateStatistics: async (personaId: string) => {
        const res = await generateStatisticsApi(personaId);
        if (res.success) {
          set({
            statistics: res.data?.statistics,
          });
        }
      },
      setTaskId: (taskId: string) => {
        set({
          taskId,
        });
      },
      generateResult: async (personaId: string, connectionId: string) => {
        const state = get();
        set({
          taskId: "",
          isFinished: false,
        });

        // 防止重复调用：如果正在为同一个 personaId 生成内容，则直接返回
        if (
          state.isGenerating &&
          state.currentGeneratingPersonaId === personaId
        ) {
          return;
        }
        // 设置生成状态
        set({
          isGenerating: true,
          currentGeneratingPersonaId: personaId,
        });
        try {
          const res = await generateApi(personaId, connectionId);
          if (res.success && res.data?.taskId) {
            set({
              taskId: res.data.taskId,
              isGenerating: false,
              currentGeneratingPersonaId: null,
            });
          } else {
            set({
              isGenerating: false,
              currentGeneratingPersonaId: null,
            });
          }
        } catch (error) {
          console.error(error);
          set({
            isGenerating: false,
            currentGeneratingPersonaId: null,
          });
        } finally {
          // 重置生成状态
          set({
            isGenerating: false,
            currentGeneratingPersonaId: null,
          });
        }
      },
      getGenerateResult: async (taskId: string) => {
        const res = await generateResultApi(taskId);
        if (res.success) {
          set({
            currentSteps: ProcessingStage.FINAL_GENERATION,
            isFinished: true,
            finalContent: res.data?.finalContent,
          });
        } else {
          if (res.error.code === "2001") {
            set({
              currentSteps: res.error?.details,
            });
          }
        }
      },
      getResultByTaskId: async (taskId: string) => {
        const res = await generateResultApi(taskId);
        if (res.success) {
          set({
            finalContent: res.data?.finalContent,
          });
        }
      },
      getGenerateList: async (personaId: string) => {
        const res = await generateGetListApi(personaId);
        if (res.success) {
          set({
            historyList: res.data?.generateLists,
          });
        }
      },
      publish: async (
        data: IGenerateList,
        taskId: string,
        personaId: string
      ) => {
        await generateUpdateApi(data, "PUBLISHED", taskId, personaId);
        await get().getResultByTaskId(taskId);
      },
      save: async (data: IGenerateList, taskId: string, personaId: string) => {
        await generateUpdateApi(data, "FAVORITES", taskId, personaId);
        await get().getResultByTaskId(taskId);
      },
      // getGenerate: async (taskId: string) => {
      //   const res = await generateGetGenerateApi(taskId);
      //   if (res.success) {
      //     set({
      //       // finalContent: res.data,
      //     });
      //   }
      // },
    })
  )
);

export default useGenerateStore;
