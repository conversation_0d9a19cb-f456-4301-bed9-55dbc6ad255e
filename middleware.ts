import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

// 需要保护的路由
const protectedRoutes = [
  "/",
  "/workspace",
  "/camera-roll",
  "/info",
  "/setting",
];

// 公开路由（不需要验证）
const publicRoutes = ["/login", "/api/auth", "/debug-auth"];

// 验证 token 的服务端函数
async function validateTokenOnServer(token: string): Promise<boolean> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/verify-token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.success === true;
  } catch (error) {
    console.error("Token validation failed:", error);
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 跳过静态文件和 API 路由（除了需要保护的）
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  // 检查是否为公开路由
  const isPublicRoute = publicRoutes.some((route) =>
    pathname.startsWith(route)
  );

  if (isPublicRoute) {
    return NextResponse.next();
  }

  // 检查是否为需要保护的路由
  const isProtectedRoute = protectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );

  if (!isProtectedRoute) {
    return NextResponse.next();
  }

  // 获取 NextAuth token
  const nextAuthToken = await getToken({
    req: request,
    secret: process.env.NEXT_PUBLIC_NEXTAUTH_SECRET,
  });

  // 检查本地存储的 token（从 cookie 或 header 中获取）
  const localToken =
    request.cookies.get("token")?.value ||
    request.headers.get("authorization")?.replace("Bearer ", "");

  // 如果没有任何 token，重定向到登录页
  if (!nextAuthToken && !localToken) {
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // 如果有 NextAuth token，检查其有效性
  if (nextAuthToken) {
    // NextAuth token 存在且有效
    if (nextAuthToken.smartiesToken) {
      // 验证后端 token
      const isValidBackendToken = await validateTokenOnServer(
        nextAuthToken.smartiesToken as string
      );
      if (!isValidBackendToken) {
        // 后端 token 无效，清除 session 并重定向
        const response = NextResponse.redirect(new URL("/login", request.url));
        response.cookies.delete("next-auth.session-token");
        response.cookies.delete("next-auth.csrf-token");
        return response;
      }
    }
    return NextResponse.next();
  }

  // 如果有本地 token，验证其有效性
  if (localToken) {
    const isValid = await validateTokenOnServer(localToken);
    if (!isValid) {
      // Token 无效，清除并重定向
      const response = NextResponse.redirect(new URL("/login", request.url));
      response.cookies.delete("token");
      return response;
    }
    return NextResponse.next();
  }

  // 默认重定向到登录页
  const loginUrl = new URL("/login", request.url);
  loginUrl.searchParams.set("callbackUrl", pathname);
  return NextResponse.redirect(loginUrl);
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api 路由 (除了需要保护的)
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - 其他静态资源
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)",
  ],
};
