import { Card } from "@/components/business/card";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * 相机胶卷页面骨架屏组件集合
 *
 * 这个文件包含了相机胶卷页面的所有骨架屏组件：
 *
 * 1. CameraRollSkeleton - 完整页面骨架屏
 *    - 包含标题区域、统计按钮组和照片网格
 *    - 支持配置是否显示统计按钮组
 *    - 支持配置照片卡片数量
 *
 * 2. PhotoGridSkeleton - 照片网格骨架屏
 *    - 仅包含照片网格部分
 *    - 适用于筛选时的局部加载状态
 *
 * 3. PhotoCardSkeleton - 单个照片卡片骨架屏
 *    - 模拟PhotoCard组件的结构
 *    - 包含图片、文件名、描述、日期、状态、标签等元素
 *
 * 使用示例：
 * ```tsx
 * // 完整页面加载
 * <CameraRollSkeleton showStatistics={true} photoCount={10} />
 *
 * // 仅网格加载（筛选时）
 * <PhotoGridSkeleton photoCount={6} />
 *
 * // 单个卡片
 * <PhotoCardSkeleton />
 * ```
 */

interface CameraRollSkeletonProps {
  /** 是否显示统计按钮组骨架屏 */
  showStatistics?: boolean;
  /** 照片卡片数量 */
  photoCount?: number;
}

/**
 * 相机胶卷页面骨架屏组件
 * 包含标题区域、统计切换按钮组和照片网格的加载状态
 */
const CameraRollSkeleton = ({
  showStatistics = true,
  photoCount = 10,
}: CameraRollSkeletonProps) => {
  return (
    <div className="grid gap-4">
      {/* 主卡片骨架屏 */}
      <Card>
        {/* 标题区域骨架屏 */}
        <div className="mb-4 flex items-center justify-between">
          {/* 主标题骨架屏 */}
          <Skeleton className="h-7 w-32" />
          {/* 提示文本骨架屏 */}
          <Skeleton className="h-4 w-24" />
        </div>

        {/* 统计切换按钮组骨架屏 */}
        {showStatistics && (
          <div className="flex flex-wrap gap-4 bg-background">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton
                key={index}
                className="h-8 rounded-md flex-none"
                style={{ width: `${60 + Math.random() * 40}px` }} // 随机宽度模拟不同长度的标签
              />
            ))}
          </div>
        )}
      </Card>

      {/* 照片网格骨架屏 */}
      <PhotoGridSkeleton photoCount={photoCount} />
    </div>
  );
};

/**
 * 照片网格骨架屏组件
 * 模拟照片卡片的网格布局
 */
const PhotoGridSkeleton = ({ photoCount = 10 }: { photoCount?: number }) => {
  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5">
      {Array.from({ length: photoCount }).map((_, index) => (
        <PhotoCardSkeleton key={index} />
      ))}
    </div>
  );
};

/**
 * 单个照片卡片骨架屏组件
 * 模拟PhotoCard的结构和布局
 */
const PhotoCardSkeleton = () => {
  return (
    <Card className="py-2" classContentName="px-2">
      <div>
        {/* 图片区域骨架屏 */}
        <div className="relative w-full h-32 sm:h-36 md:h-40 lg:h-44 xl:h-48 overflow-hidden rounded-t-lg">
          <Skeleton className="w-full h-full" />
        </div>

        {/* 内容区域骨架屏 */}
        <div className="flex flex-col mt-2 gap-2 sm:gap-3 p-2 sm:p-3">
          {/* 文件名骨架屏 */}
          <Skeleton className="h-4 w-3/4" />

          {/* 描述内容骨架屏 - 仅在sm及以上显示 */}
          <div className="hidden sm:block space-y-1">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>

          {/* 底部信息区域骨架屏 */}
          <div className="flex items-center justify-between">
            {/* 日期骨架屏 */}
            <Skeleton className="h-3 w-20" />
            {/* 状态标签骨架屏 */}
            <Skeleton className="h-5 w-12 rounded-full" />
          </div>

          {/* 标签区域骨架屏 */}
          <div className="flex gap-2 flex-wrap">
            {Array.from({ length: 2 }).map((_, index) => (
              <Skeleton key={index} className="h-5 w-12 rounded-full" />
            ))}
          </div>

          {/* 类型标签骨架屏 */}
          <div>
            <Skeleton className="h-5 w-16 rounded-md" />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CameraRollSkeleton;
export { PhotoGridSkeleton, PhotoCardSkeleton };
