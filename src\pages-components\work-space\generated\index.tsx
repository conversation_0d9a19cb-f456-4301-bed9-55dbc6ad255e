"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { useAppStore } from "@/store/useAppStore";
import ContentGrid from "./components/ContentGrid";
import GeneratedHeader from "./components/GeneratedHeader";
import GeneratedSummary from "./components/GeneratedSummary";
import TwitterConnectModal from "./components/TwitterConnectModal";
import { useGeneratedContent } from "./hooks/useGeneratedContent";

const Generated = () => {
  const { currentLanguage } = useLanguage();
  const { setHeaderSlot } = useAppStore();
  const router = useRouter();

  // 使用自定义 Hook 管理生成内容
  const {
    finalContent,
    handlePublish,
    handleSave,
    isTwitterModal,
    setIsTwitterModal,
    twitterContent,
  } = useGeneratedContent();

  // 设置页面头部操作按钮
  useEffect(() => {
    setHeaderSlot(<GeneratedHeader router={router} />);
    return () => {
      setHeaderSlot(null);
    };
  }, [setHeaderSlot, router]);

  return (
    <div className="space-y-6">
      <GeneratedSummary
        contentCount={finalContent.length}
        currentLanguage={currentLanguage}
      />

      <ContentGrid
        finalContent={finalContent}
        currentLanguage={currentLanguage}
        onPublish={handlePublish}
        onSave={handleSave}
      />
      <TwitterConnectModal
        open={isTwitterModal}
        twitterContent={twitterContent}
        onClose={() => setIsTwitterModal(false)}
      />
    </div>
  );
};

export default Generated;
