import { useState, useCallback, useEffect } from "react";
import type {
  FileData,
  UploadConfig,
  ChunkUploadConfig,
  FileItem,
} from "../types";
import { getFileCategory, formatTime } from "../utils/fileUtils";
import { validateFile } from "../utils/fileValidation";
import { useChunkUpload } from "./useChunkUpload";

export const useFileUpload = (
  config?: UploadConfig,
  chunkConfig?: ChunkUploadConfig
) => {
  const [files, setFiles] = useState<FileData[]>([]);

  // 集成分片上传功能
  const {
    uploadProgress: chunkUploadProgress,
    uploading: chunkUploading,
    uploadFiles: uploadChunkedFiles,
    cancelUpload,
    clearProgress,
  } = useChunkUpload(chunkConfig);

  // 实时同步分片上传进度到文件状态
  useEffect(() => {
    setFiles((prev) =>
      prev.map((f) => {
        const uploadProgress = chunkUploadProgress[f.id];
        if (uploadProgress) {
          return {
            ...f,
            status:
              uploadProgress.status === "success"
                ? "success"
                : uploadProgress.status === "error"
                ? "error"
                : uploadProgress.status === "uploading"
                ? "uploading"
                : f.status,
            progress: uploadProgress.progress,
            uploadSpeed: uploadProgress.uploadSpeed,
            remainingTime: uploadProgress.remainingTime,
            fileKey: uploadProgress.fileKey || f.fileKey, // 同步fileKey
          };
        }
        return f;
      })
    );
  }, [chunkUploadProgress]);

  // 处理文件选择
  const processFiles = useCallback(
    (fileList: FileList) => {
      const filesArray = Array.from(fileList);
      const maxFiles = config?.maxFiles || 5; // 默认最多5个文件

      // 检查文件数量限制
      setFiles((prev) => {
        const currentFileCount = prev.length;

        // 如果超过限制，只处理允许的文件数量
        const allowedNewFiles = maxFiles - currentFileCount;
        if (allowedNewFiles <= 0) {
          // 如果已经达到最大文件数，显示提示
          if (process.env.NODE_ENV === "development") {
            console.warn(`最多只能上传 ${maxFiles} 个文件`);
          }
          return prev;
        }

        const filesToProcess = filesArray.slice(0, allowedNewFiles);
        if (filesToProcess.length < filesArray.length) {
          if (process.env.NODE_ENV === "development") {
            console.warn(
              `最多只能上传 ${maxFiles} 个文件，已自动限制为前 ${allowedNewFiles} 个文件`
            );
          }
        }

        const validFiles = filesToProcess.map((file: File) => {
          const errors = validateFile(file, config);
          const category = getFileCategory(file);

          const fileData: FileData = {
            file,
            id: Math.random().toString(36).substring(2, 11),
            name: file.name,
            size: file.size,
            type: file.type,
            category: category || "other",
            errors,
            status: errors.length > 0 ? "error" : "pending",
            uploadTime: formatTime(new Date().toLocaleString()),
            progress: 0,
            controller: new AbortController(),
          };

          // 为图片文件创建预览（如果配置允许）
          if (
            category === "image" &&
            errors.length === 0 &&
            config?.showPreview !== false
          ) {
            fileData.preview = URL.createObjectURL(file);
          }

          return fileData;
        });

        return [...prev, ...validFiles];
      });
    },
    [config]
  );

  // 上传处理
  const handleUpload = async () => {
    const pendingFiles = files.filter(
      (f) => f.status === "pending" && f.errors.length === 0
    );
    if (pendingFiles.length === 0) {
      return;
    }

    // 立即将待上传文件状态设置为 "uploading"，避免重复点击
    setFiles((prev) =>
      prev.map((f) => {
        if (f.status === "pending" && f.errors.length === 0) {
          return { ...f, status: "uploading" as const };
        }
        return f;
      })
    );

    try {
      // 转换为 FileItem 格式
      const fileItems: FileItem[] = pendingFiles.map((fileData) => ({
        file: fileData.file,
        id: fileData.id,
        controller: fileData.controller,
      }));

      // 使用分片上传 - 状态完全由 useChunkUpload 管理
      await uploadChunkedFiles(fileItems);
    } catch (error) {
      console.error("Upload failed:", error);
      // 如果上传失败，将状态重置为 pending
      setFiles((prev) =>
        prev.map((f) => {
          if (pendingFiles.some((pf) => pf.id === f.id)) {
            return { ...f, status: "pending" as const };
          }
          return f;
        })
      );
    }
  };

  const removeFile = (fileId: string) => {
    setFiles((prev) => {
      const fileToRemove = prev.find((f) => f.id === fileId);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      // 取消上传
      if (fileToRemove?.controller) {
        fileToRemove.controller.abort();
      }
      return prev.filter((f) => f.id !== fileId);
    });

    // 取消分片上传
    cancelUpload(fileId);
  };

  const clearAll = () => {
    files.forEach((file) => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
      // 取消所有上传
      if (file.controller) {
        file.controller.abort();
      }
    });
    setFiles([]);
    clearProgress();
  };

  // 按类型分组文件
  const groupedFiles = files.reduce((acc: Record<string, FileData[]>, file) => {
    const category = file.category || "other";
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(file);
    return acc;
  }, {});

  return {
    files,
    uploading: chunkUploading,
    groupedFiles,
    uploadProgress: chunkUploadProgress,
    processFiles,
    handleUpload,
    removeFile,
    clearAll,
    cancelUpload,
  };
};
