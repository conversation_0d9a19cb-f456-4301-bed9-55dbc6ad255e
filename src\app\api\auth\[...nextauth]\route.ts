/* eslint-disable @typescript-eslint/no-explicit-any */
import { LambdaClient, InvokeCommand } from "@aws-sdk/client-lambda";
import NextAuth, { AuthOptions } from "next-auth";
import { JWT } from "next-auth/jwt";
import GoogleProvider from "next-auth/providers/google";

// Lambda客户端配置
const createLambdaClient = () => {
  const accessKeyId = process.env.NEXT_PUBLIC_AWS_ACCESS_KEY_ID;
  const secretAccessKey = process.env.NEXT_PUBLIC_AWS_SECRET_ACCESS_KEY;

  if (!accessKeyId || !secretAccessKey) {
    console.error("AWS credentials are not properly configured");
    return null;
  }

  return new LambdaClient({
    region: "ap-southeast-1",
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });
};

const lambdaClient = createLambdaClient();

// 验证必需的环境变量
if (process.env.NODE_ENV === "production") {
  if (!process.env.NEXT_PUBLIC_NEXTAUTH_SECRET) {
    throw new Error(
      "NEXT_PUBLIC_NEXTAUTH_SECRET must be set in production. " +
        "Generate a secret with: openssl rand -base64 32"
    );
  }
  if (!process.env.NEXT_PUBLIC_NEXTAUTH_URL) {
    process.env.NEXTAUTH_URL = "https://kaspum.fun";
    console.warn(
      "NEXTAUTH_URL is not set. This may cause issues in production."
    );
  } else {
    process.env.NEXTAUTH_URL = process.env.NEXT_PUBLIC_NEXTAUTH_URL;
  }
}

// NextAuth配置
const authOptions: AuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
      clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
      httpOptions: {
        timeout: 50000,
      },
    }),
  ],
  debug: process.env.NODE_ENV === "development",
  secret:
    process.env.NEXT_PUBLIC_NEXTAUTH_SECRET ||
    "fallback-secret-for-development",
  pages: {
    signIn: "/login", // 自定义登录页面
    error: "/login",
  },
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, account, profile }: any): Promise<JWT> {
      if (account && profile) {
        try {
          // 检查必需的环境变量和客户端
          const lambdaFunctionName =
            process.env.NEXT_PUBLIC_LAMBDA_FUNCTION_NAME;
          if (!lambdaFunctionName) {
            console.error("NEXT_PUBLIC_LAMBDA_FUNCTION_NAME is not set");
            token.smartiesToken = "";
            token.userId = "";
            return token;
          }

          if (!lambdaClient) {
            console.error("Lambda client is not properly configured");
            token.smartiesToken = "";
            token.userId = "";
            return token;
          }

          // 调用 Lambda 函数来处理用户数据
          const lambdaParams = {
            FunctionName: lambdaFunctionName,
            Payload: JSON.stringify({
              email: profile.email,
              name: profile.name,
              image: profile.picture,
              provider: account.provider,
            }),
          };

          const command = new InvokeCommand(lambdaParams);
          const result = await lambdaClient.send(command);

          // 检查 Lambda 响应
          if (!result.Payload) {
            console.error("Lambda function returned no payload");
            token.smartiesToken = "";
            token.userId = "";
            return token;
          }

          // 解析 Lambda 返回的数据
          const response = JSON.parse(new TextDecoder().decode(result.Payload));
          if (response.success) {
            token.smartiesToken = response.token;
            token.userId = response.userId;
            token.user = response.user;
          } else {
            console.error("Lambda function failed:", response);
            token.smartiesToken = "";
            token.userId = "";
          }
        } catch (error) {
          console.error("Error calling Lambda function:", error);
          token.smartiesToken = "";
          token.userId = "";
        }
      }
      return token;
    },
    async session({ session, token }: { session: any; token: any }) {
      try {
        if (token) {
          session.smartiesToken = token.smartiesToken;
          session.userId = token.userId;
          session.user = {
            ...session.user,
            id: token.userId,
            ...token.user,
          };
        }
        return session;
      } catch (error) {
        console.error("Session callback error:", error);
        // Return a minimal session to prevent 500 error
        return {
          ...session,
          smartiesToken: null,
          userId: null,
        };
      }
    },
    async redirect({ url, baseUrl }) {
      // 登录成功后重定向到主页
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`;
      } else if (new URL(url).origin === baseUrl) {
        return url;
      }
      return baseUrl;
    },
  },
};

// App Router API处理函数
const handler = NextAuth(authOptions);

// 添加错误处理包装器
const wrappedHandler = async (req: Request, context: any) => {
  try {
    return await handler(req, context);
  } catch (error) {
    console.error("NextAuth handler error:", error);
    return new Response(
      JSON.stringify({
        error: "Authentication service temporarily unavailable",
        details: process.env.NODE_ENV === "development" ? error : undefined,
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
};

export { wrappedHandler as GET, wrappedHandler as POST };
