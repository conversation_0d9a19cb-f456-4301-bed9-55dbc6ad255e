import dynamic from "next/dynamic";
import { PreloadWrapper } from "@/components/PreloadWrapper";

const KnowledgeBase = dynamic(
  () => import("@/pages-components/information/knowledge-base"),
  {
    loading: () => <div>Loading...</div>,
  }
);

export default function KnowledgeBasePage() {
  return (
    <PreloadWrapper routes={["/info/source", "/info/custom-source"]}>
      <KnowledgeBase />
    </PreloadWrapper>
  );
}
