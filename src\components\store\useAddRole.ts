import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

interface AddRoleStore {
  addRole: {
    roleName: string;
    gender: string;
    mbtiType: string;
    avatar: string;
    personality: string;
    roleIntro: string;
    topicPreference: string[];
  };
}
const useAddRoleStore = create<AddRoleStore>()(
  immer(
    (): AddRoleStore => ({
      addRole: {} as AddRoleStore["addRole"],
    })
  )
);

export default useAddRoleStore;
