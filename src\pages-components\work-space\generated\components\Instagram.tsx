"use client";

import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  PaperAirplaneIcon,
  BookmarkIcon,
  EllipsisHorizontalIcon,
} from "@heroicons/react/24/outline";
import { useMemo } from "react";
import { ProfileIcon } from "@/components/ProfileIcon";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImageCarousel } from "@/components/ui/ImageCarousel";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useRoleStore from "@/store/persona";

interface InstagramProps {
  className?: string;
  data: IGenerateList;
  onPublish?: (data: IGenerateList) => void;
  onSave?: (data: IGenerateList) => void;
}

const Instagram = ({ className, data, onPublish, onSave }: InstagramProps) => {
  const { currentRole } = useRoleStore();
  const { t } = useLanguage();

  // 图片相关的计算属性
  const photos = useMemo(() => data.photos || [], [data.photos]);
  return (
    <div>
      <Card
        className={`w-full mx-auto  border-border hover:border-ring gap-4 ${className}`}
      >
        {/* 用户信息头部 */}
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center gap-3">
            <Avatar className="w-8 h-8">
              <AvatarImage src="" alt="Emma" />
              <AvatarFallback className="bg-red-500 text-white font-semibold text-sm">
                <ProfileIcon
                  seed={currentRole.personaId}
                  size={60}
                ></ProfileIcon>
              </AvatarFallback>
            </Avatar>
            <span className="font-semibold text-sm">{currentRole.name}</span>
          </div>
          <EllipsisHorizontalIcon className="w-6 h-6 text-gray-600" />
        </div>

        {/* 图片区域 */}
        <ImageCarousel
          photos={photos}
          aspectRatio="aspect-square"
          showArrows={true}
          showPageIndicator={true}
          showDots={false}
          enableKeyboardNavigation={true}
          enablePreloading={true}
          placeholderIconSize="w-16 h-16"
        />

        {/* 互动按钮区域 */}
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center gap-4">
            <HeartIcon className="w-6 h-6  hover:text-red-500 cursor-pointer" />
            <ChatBubbleOvalLeftIcon className="w-6 h-6  hover:text-red-500 cursor-pointer" />
            <PaperAirplaneIcon className="w-6 h-6  hover:text-red-500 cursor-pointer" />
          </div>
          <BookmarkIcon className="w-6 h-6  hover:text-red-500 cursor-pointer" />
        </div>

        {/* 点赞数 */}
        <div className="px-3">
          <span className="font-semibold text-sm">2.3k likes</span>
        </div>

        {/* 内容描述 */}
        <div className="px-3">
          <div className="text-sm">
            <div className="font-semibold mb-1">{data.title}</div>
            <span>{data.finalContent}</span>
          </div>

          {/* 标签 */}
          <div className="mt-2 text-sm">
            {data.tags?.map((item) => (
              <span
                key={item}
                className="text-blue-900 hover:underline cursor-pointer ml-1"
              >
                #{item}
              </span>
            ))}
          </div>

          {/* 发布时间 */}
          <div className="mt-2 text-xs text-gray-500">
            {new Date(data.createdAt || Date.now()).toLocaleDateString(
              "zh-CN",
              {
                year: "numeric",
                month: "short",
                day: "numeric",
              }
            )}
          </div>
        </div>
      </Card>
      <div className="flex gap-4 justify-center mt-4">
        <Button
          size="sm"
          variant="outline"
          disabled={data.status === "PUBLISHED"}
          onClick={() => {
            onPublish?.(data);
          }}
          className="hover:bg-red-600 text-white px-6 py-1 rounded-full text-sm font-medium"
        >
          {data.status === "PUBLISHED"
            ? t("button:published")
            : t("button:publish")}
        </Button>
        <Button
          size="sm"
          disabled={data.status === "FAVORITES" || data.status === "PUBLISHED"}
          variant="outline"
          onClick={() => {
            onSave?.(data);
          }}
          className="text-white hover:bg-surface-light px-6 py-1 rounded-full text-sm font-medium"
        >
          {data.status === "FAVORITES" ? t("button:saved") : t("button:save")}
        </Button>
      </div>
    </div>
  );
};

export default Instagram;
