"use client";

import { useRouter } from "next/navigation";
import { useCallback, useEffect } from "react";
import useWebSocketWithReconnection from "@/hooks/useSocket";
import { StepCard } from "@/pages-components/work-space/generating/components/StepCard";
import { NAVIGATION_DELAY } from "@/pages-components/work-space/generating/constants";
import { useGeneratingHeader } from "@/pages-components/work-space/generating/hooks/useGeneratingHeader";
import { useGeneratingPolling } from "@/pages-components/work-space/generating/hooks/useGeneratingPolling";
import { useGeneratingSteps } from "@/pages-components/work-space/generating/hooks/useGeneratingSteps";
import useGenerateStore, { ProcessingStage } from "@/store/generate";
import useRoleStore from "@/store/persona";
import useSourceStore from "@/store/source";

/**
 * 步骤处理状态枚举
 */
export enum Processing {
  INIT = "INIT",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
}

/**
 * WebSocket消息类型定义
 */
interface WebSocketMessage {
  type: string;
  data: {
    type: "start" | "thinking" | "complete";
    message?: string;
    content?: string;
    taskId?: string;
  };
}

/**
 * 生成页面主组件
 *
 * 功能：
 * - 显示内容生成的三个步骤：用户分析、知识搜索、内容生成
 * - 通过WebSocket实时接收生成进度和思考过程
 * - 管理页面头部和轮询逻辑
 */
const Generating = () => {
  const {
    currentSteps,
    setThinkingData,
    thinkingData,
    setSteps,
    setThinkingDataToInit,
  } = useGenerateStore();
  const { currentRole } = useRoleStore();
  const router = useRouter();
  const { fileList, getFileList } = useSourceStore();

  // 获取文件列表
  useEffect(() => {
    if (currentRole?.personaId) {
      getFileList(currentRole.personaId);
    }
  }, [currentRole?.personaId, getFileList]);

  // WebSocket消息处理
  const onMessage = useCallback(
    (message: WebSocketMessage) => {
      if (message.type === "search") {
        const { data } = message;

        switch (data.type) {
          case "start":
            if (data.message) {
              // setThinkingData(data.message);
            }
            break;
          case "thinking":
            if (data.content) {
              setThinkingData(data.content);
            }
            break;
          case "complete":
            if (data.taskId) {
              setSteps(ProcessingStage.INIT);
              setThinkingDataToInit();
              // 导航到生成结果页面
              setTimeout(() => {
                router.push(`/workspace/generated?taskId=${data.taskId}`);
              }, NAVIGATION_DELAY);
            }
            break;
        }
      }
    },
    [setThinkingData, router]
  );

  // WebSocket连接管理
  const { connectionId, startWebSocket, closeWebSocket } =
    useWebSocketWithReconnection({
      onMessage,
    });

  // 初始化WebSocket连接
  useEffect(() => {
    startWebSocket();
    return () => {
      closeWebSocket();
    };
  }, [startWebSocket, closeWebSocket]);

  // 使用自定义Hooks管理页面逻辑
  useGeneratingHeader();
  useGeneratingPolling(connectionId as string);

  // 使用步骤管理Hook
  const { steps } = useGeneratingSteps({
    currentSteps,
    currentRole,
    fileList,
    thinkingData,
  });

  // 渲染步骤列表
  return (
    <div className="space-y-6">
      {steps.map((step, index) => (
        <StepCard key={`step-${index}-${step.title}`} step={step} />
      ))}
    </div>
  );
};

export default Generating;
