import { Upload } from "lucide-react";
import { useLanguage } from "@/hooks/useLanguage";
import type { DropzoneAreaProps } from "../types";
import { getAllowedExtensions } from "../utils/uploadConfig";

export const DropzoneArea = ({
  isDragActive,
  isDragAccept,
  isDragReject,
  onDragEnter,
  onDragLeave,
  onDragOver,
  onDrop,
  onClick,
  fileInputRef,
  onFileSelect,
  uploading: _uploading,
  config,
  currentFileCount = 0,
}: DropzoneAreaProps) => {
  const { t } = useLanguage();
  const maxFiles = config.maxFiles || 5;
  const isMaxFilesReached = currentFileCount >= maxFiles;

  const getDropzoneStyle = () => {
    const baseStyle =
      "border-2 border-dashed rounded-lg p-12 text-center transition-all duration-200 bg-surface-light";

    // 如果达到最大文件数，显示禁用状态
    if (isMaxFilesReached) {
      return `${baseStyle} border-border bg-surface cursor-not-allowed opacity-60`;
    }

    const cursorStyle = "cursor-pointer";
    if (isDragAccept) {
      return `${baseStyle} ${cursorStyle} border-success bg-success-alpha-10`;
    }
    if (isDragReject) {
      return `${baseStyle} ${cursorStyle} border-error bg-error-alpha-10`;
    }
    if (isDragActive) {
      return `${baseStyle} ${cursorStyle} border-primary bg-primary-alpha-10`;
    }
    return `${baseStyle} ${cursorStyle} border-border hover:border-ring hover:bg-primary-alpha-5`;
  };

  const handleClick = () => {
    if (!isMaxFilesReached) {
      onClick();
    }
  };

  const handleDragEvents = (e: React.DragEvent) => {
    if (isMaxFilesReached) {
      e.preventDefault();
      return;
    }
  };

  return (
    <div
      className={getDropzoneStyle()}
      onDragEnter={isMaxFilesReached ? handleDragEvents : onDragEnter}
      onDragLeave={isMaxFilesReached ? handleDragEvents : onDragLeave}
      onDragOver={isMaxFilesReached ? handleDragEvents : onDragOver}
      onDrop={isMaxFilesReached ? handleDragEvents : onDrop}
      onClick={handleClick}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={getAllowedExtensions(config).join(",")}
        onChange={onFileSelect}
        style={{ display: "none" }}
      />

      <div className="text-text-secondary mb-4">
        <Upload className="w-16 h-16 mx-auto mb-4" />
      </div>

      {isDragActive ? (
        <div>
          <p className="text-xl font-medium mb-2">
            {isDragAccept ? (
              <span className="text-success">
                {t("upload:dropzone.dragActive.accept")}
              </span>
            ) : (
              <span className="text-error">
                {t("upload:dropzone.dragActive.reject")}
              </span>
            )}
          </p>
        </div>
      ) : (
        <div>
          {isMaxFilesReached ? (
            <div>
              <p className="text-xl font-medium text-text-secondary mb-2">
                已达到最大文件数量限制
              </p>
              <p className="text-text-tertiary text-sm">
                最多支持 {maxFiles} 个文件，请删除部分文件后再添加
              </p>
            </div>
          ) : (
            <div>
              <p className="text-xl font-medium text-text-primary mb-2">
                {t("upload:dropzone.title")}
              </p>
              {(config.mode === "image" || config.mode === "mixed") && (
                <p className="text-text-secondary text-sm">
                  {t("upload:dropzone.subtitle.image")}
                </p>
              )}
              {(config.mode === "document" || config.mode === "mixed") && (
                <p className="text-text-secondary text-sm">
                  {t("upload:dropzone.subtitle.document")}
                </p>
              )}
            </div>
          )}
          <p className="text-text-tertiary text-xs mt-2">
            最多支持 {maxFiles} 个文件 ({currentFileCount}/{maxFiles})
          </p>
        </div>
      )}
    </div>
  );
};
