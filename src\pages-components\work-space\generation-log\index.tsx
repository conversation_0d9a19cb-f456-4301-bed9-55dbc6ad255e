"use client";
import clsx from "clsx";
import { Loader } from "lucide-react";
import { useEffect, useState } from "react";
import { Card } from "@/components/business/card";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { DialogModal } from "@/components/business/modal";
import { Select } from "@/components/business/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useGenerateStore from "@/store/generate";
import useRoleStore from "@/store/persona";
import Twitter from "../generated/components/Twitter";
import Xhs from "../generated/components/Xhs";

const GenerationLog = () => {
  const { t, currentLanguage } = useLanguage();
  const {
    getGenerateList,
    historyList,
    publish: publishToStore,
  } = useGenerateStore();
  const { currentRole } = useRoleStore();
  const [filterList, setFilterList] = useState(historyList);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<IGenerateList | null>(null);
  const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getGenerateList(currentRole.personaId);
    }
  }, [currentRole, getGenerateList]);
  useEffect(() => {
    setFilterList(historyList);
  }, [historyList]);

  // 处理查看按钮点击
  const handleViewClick = (item: IGenerateList) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };
  const handlePublish = async (item: IGenerateList) => {
    setIsFetching(true);
    await publishToStore(item, item.taskId as string, currentRole.personaId);
    await getGenerateList(currentRole.personaId);
    setIsFetching(false);
  };
  // 关闭弹窗
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  // 渲染平台组件
  const renderPlatformComponent = () => {
    if (!selectedItem) {
      return null;
    }

    const commonProps = {
      data: selectedItem,
      isShowButton: false,
      onPublish: async () => {}, // 在弹窗中可以不提供发布功能，或者根据需要实现
      onSave: async () => {},
    };

    switch (currentLanguage) {
      case "zh-CN":
        return <Xhs {...commonProps} />;
      default:
        return <Twitter {...commonProps} />;
    }
  };

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("workspace:generationLog.title")}
        <Select
          onValueChange={(value) => {
            // 前端过滤
            if (value === "all") {
              setFilterList(historyList);
            } else {
              setFilterList(
                historyList.filter((item) => item.status === value)
              );
            }
          }}
          className="!h-10"
          placeholder={t("button:viewAll")}
          options={[
            { name: t("button:all"), value: "all" },
            { name: t("button:published"), value: "PUBLISHED" },
            { name: t("button:unpublish"), value: "FAVORITES" },
          ]}
        ></Select>
      </Text>
      <div
        className={clsx(
          "grid gap-4  max-md:grid-cols-1",
          filterList.length === 0 ? "grid-cols-1" : "grid-cols-2"
        )}
      >
        {filterList.length === 0 && <EmptyState type={EmptyType.NO_CONTENT} />}
        {filterList.map((item) => (
          <Card
            key={item.id}
            className="bg-surface-light hover:border-ring hover:shadow-glow-lg"
          >
            <div>
              <Text
                variant={TextVariant.BODY_MEDIUM}
                className="flex justify-between items-center mb-5"
              >
                {item.title || "无标题"}
                <Badge
                  variant={
                    item.status === "PUBLISHED" ? "default" : "secondary"
                  }
                >
                  {item.status === "PUBLISHED"
                    ? t("button:published")
                    : t("button:unpublish")}
                </Badge>
              </Text>
              <Text
                variant={TextVariant.BODY_SMALL}
                className="text-text-secondary  border-b border-border pb-2"
              >
                <b className="line-clamp-2">{item.finalContent}</b>
              </Text>
              <Text
                variant={TextVariant.CAPTION}
                className="text-text-tertiary mt-4  flex items-center gap-4 justify-between"
              >
                <div className="flex-1 flex gap-2 flex-wrap">
                  {item.tags?.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      #{tag}
                    </Badge>
                  ))}
                </div>
                <div className="self-start">
                  {new Date(item.createdAt as string).toLocaleDateString()}
                </div>
              </Text>
              <div className="mt-6 flex gap-4 justify-center">
                <Button
                  size="sm"
                  variant="secondary"
                  className="text-sm text-text-secondary"
                  onClick={() => handleViewClick(item)}
                >
                  {t("button:view")}
                </Button>
                {item.status !== "PUBLISHED" && (
                  <Button
                    size="sm"
                    disabled={isFetching}
                    className="text-sm"
                    onClick={() => handlePublish(item)}
                  >
                    {isFetching && <Loader className="size-4 spin360"></Loader>}
                    {t("button:publish")}
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* 查看详情弹窗 */}
      <DialogModal
        open={isModalOpen}
        openChange={handleCloseModal}
        title={selectedItem?.title || t("workspace:generationLog.viewDetail")}
        className="w-[600px] max-w-[1000px]"
      >
        <div className="space-y-6">
          <div className="flex justify-center">{renderPlatformComponent()}</div>
        </div>
      </DialogModal>
    </Card>
  );
};

export default GenerationLog;
