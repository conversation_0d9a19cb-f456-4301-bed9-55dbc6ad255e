import type { UploadConfig, UploadMode } from "../types";
import { FILE_CONFIGS } from "./fileConfig";

// 默认配置
export const DEFAULT_UPLOAD_CONFIG: UploadConfig = {
  mode: "mixed",
  accept: {
    image: [...FILE_CONFIGS.image.types],
    document: [...FILE_CONFIGS.document.types],
  },
  maxSize: {
    image: FILE_CONFIGS.image.maxSize,
    document: FILE_CONFIGS.document.maxSize,
  },
  multiple: true,
  maxFiles: 5, // 默认最多支持5个文件
  showPreview: true,
  showProgress: true,
};

// 预设配置
export const PRESET_CONFIGS: Record<UploadMode, UploadConfig> = {
  image: {
    mode: "image",
    accept: {
      image: [...FILE_CONFIGS.image.types],
    },
    maxSize: {
      image: FILE_CONFIGS.image.maxSize,
    },
    multiple: true,
    maxFiles: 5, // 图片模式最多5个文件
    showPreview: true,
    showProgress: true,
  },
  document: {
    mode: "document",
    accept: {
      document: [...FILE_CONFIGS.document.types],
    },
    maxSize: {
      document: FILE_CONFIGS.document.maxSize,
    },
    multiple: true,
    maxFiles: 5, // 文档模式最多5个文件
    showPreview: false,
    showProgress: true,
  },
  mixed: DEFAULT_UPLOAD_CONFIG,
};

// 合并配置的工具函数
export const mergeUploadConfig = (
  userConfig?: Partial<UploadConfig>
): UploadConfig => {
  if (!userConfig) {
    return DEFAULT_UPLOAD_CONFIG;
  }

  const baseConfig = PRESET_CONFIGS[userConfig.mode || "mixed"];

  return {
    ...baseConfig,
    ...userConfig,
    accept: {
      ...baseConfig.accept,
      ...userConfig.accept,
    },
    maxSize: {
      ...baseConfig.maxSize,
      ...userConfig.maxSize,
    },
  };
};

// 获取允许的文件扩展名
export const getAllowedExtensions = (config: UploadConfig): string[] => {
  const extensions: string[] = [];

  if (config.mode === "image" || config.mode === "mixed") {
    extensions.push(...FILE_CONFIGS.image.extensions);
  }

  if (config.mode === "document" || config.mode === "mixed") {
    extensions.push(...FILE_CONFIGS.document.extensions);
  }

  return extensions;
};

// 获取允许的 MIME 类型
export const getAllowedMimeTypes = (config: UploadConfig): string[] => {
  const mimeTypes: string[] = [];

  if (
    config.accept?.image &&
    (config.mode === "image" || config.mode === "mixed")
  ) {
    mimeTypes.push(...config.accept.image);
  }

  if (
    config.accept?.document &&
    (config.mode === "document" || config.mode === "mixed")
  ) {
    mimeTypes.push(...config.accept.document);
  }

  return mimeTypes;
};

// 检查文件是否被配置允许
export const isFileAllowed = (file: File, config: UploadConfig): boolean => {
  const allowedMimeTypes = getAllowedMimeTypes(config);
  return allowedMimeTypes.includes(file.type);
};
