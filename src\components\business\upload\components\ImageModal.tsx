import {
  X,
  ZoomIn,
  <PERSON>mOut,
  RotateCw,
  RotateCcw,
  Maximize2,
  Download,
  Move,
} from "lucide-react";
import { useState, useCallback, useEffect } from "react";
import { createPortal } from "react-dom";
import { useLanguage } from "@/hooks/useLanguage";
import type { ImageModalProps } from "../types";

interface ImageTransform {
  scale: number;
  rotation: number;
  translateX: number;
  translateY: number;
}

export const ImageModal = ({ src, alt, onClose }: ImageModalProps) => {
  const { t } = useLanguage();
  const [transform, setTransform] = useState<ImageTransform>({
    scale: 1,
    rotation: 0,
    translateX: 0,
    translateY: 0,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 避免未使用变量警告
  void isFullscreen;

  // 确保组件已挂载
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // 重置变换
  const resetTransform = useCallback(() => {
    setTransform({
      scale: 1,
      rotation: 0,
      translateX: 0,
      translateY: 0,
    });
  }, []);

  // 缩放
  const handleZoom = useCallback((delta: number) => {
    setTransform((prev) => ({
      ...prev,
      scale: Math.max(0.1, Math.min(5, prev.scale + delta)),
    }));
  }, []);

  // 旋转
  const handleRotate = useCallback((degrees: number) => {
    setTransform((prev) => ({
      ...prev,
      rotation: (prev.rotation + degrees) % 360,
    }));
  }, []);

  // 鼠标拖拽开始
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (e.button === 0) {
        // 左键
        setIsDragging(true);
        setDragStart({
          x: e.clientX - transform.translateX,
          y: e.clientY - transform.translateY,
        });
      }
    },
    [transform.translateX, transform.translateY]
  );

  // 鼠标拖拽
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        setTransform((prev) => ({
          ...prev,
          translateX: e.clientX - dragStart.x,
          translateY: e.clientY - dragStart.y,
        }));
      }
    },
    [isDragging, dragStart]
  );

  // 鼠标拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 鼠标滚轮缩放
  const handleWheel = useCallback(
    (e: WheelEvent) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      handleZoom(delta);
    },
    [handleZoom]
  );

  // 键盘快捷键
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      switch (e.key) {
        case "Escape":
          onClose();
          break;
        case "=":
        case "+":
          handleZoom(0.1);
          break;
        case "-":
          handleZoom(-0.1);
          break;
        case "r":
        case "R":
          handleRotate(90);
          break;
        case "0":
          resetTransform();
          break;
      }
    },
    [onClose, handleZoom, handleRotate, resetTransform]
  );

  // 下载图片
  const handleDownload = useCallback(() => {
    const link = document.createElement("a");
    link.href = src;
    link.download = alt || "image";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [src, alt]);

  // 全屏切换
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // 事件监听
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    document.addEventListener("wheel", handleWheel, { passive: false });
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("fullscreenchange", handleFullscreenChange);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.removeEventListener("wheel", handleWheel);
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, [handleMouseMove, handleMouseUp, handleWheel, handleKeyDown]);

  // 生成变换样式
  const imageStyle = {
    transform: `translate(${transform.translateX}px, ${transform.translateY}px) scale(${transform.scale}) rotate(${transform.rotation}deg)`,
    cursor: isDragging ? "grabbing" : "grab",
    transition: isDragging ? "none" : "transform 0.2s ease-out",
  };

  if (!mounted) {
    return null;
  }

  const modalContent = (
    <div
      className="fixed inset-0 bg-black-alpha-80 flex items-center justify-center z-50"
      onClick={(e) => {
        // 只有点击背景时才关闭，不是点击子元素
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      {/* 工具栏 */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-[60]">
        <div
          className="flex items-center space-x-2 bg-black-alpha-80 rounded-lg px-4 py-2 backdrop-blur-sm"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {/* 缩放控制 */}
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleZoom(-0.1);
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={`${t("button:zoomOut")} (-)`}
          >
            <ZoomOut className="w-5 h-5" />
          </button>

          <span className="text-white text-sm min-w-[60px] text-center">
            {Math.round(transform.scale * 100)}%
          </span>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleZoom(0.1);
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={`${t("button:zoomIn")} (+)`}
          >
            <ZoomIn className="w-5 h-5" />
          </button>

          <div className="w-px h-6 bg-white-alpha-30 mx-2" />

          {/* 旋转控制 */}
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleRotate(-90);
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={`${t("button:rotateLeft")} (Q)`}
          >
            <RotateCcw className="w-5 h-5" />
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleRotate(90);
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={`${t("button:rotateRight")} (R)`}
          >
            <RotateCw className="w-5 h-5" />
          </button>

          <div className="w-px h-6 bg-white-alpha-30 mx-2" />

          {/* 其他操作 */}
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              resetTransform();
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={`${t("button:reset")} (0)`}
          >
            <Move className="w-5 h-5" />
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              toggleFullscreen();
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={t("button:fullscreen")}
          >
            <Maximize2 className="w-5 h-5" />
          </button>

          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleDownload();
            }}
            className="p-2 text-white hover:text-red-500 hover:bg-gray-700 transition-colors cursor-pointer"
            style={{ pointerEvents: "auto" }}
            title={t("button:download")}
          >
            <Download className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 关闭按钮 */}
      <button
        type="button"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onClose();
        }}
        className="absolute top-4 right-4 text-white hover:text-red-500 hover:bg-gray-700 z-[100] p-2 bg-black-alpha-80 rounded-lg backdrop-blur-sm transition-colors cursor-pointer"
        style={{ pointerEvents: "auto" }}
        title={`${t("button:close")} (ESC)`}
      >
        <X className="w-6 h-6" />
      </button>

      {/* 图片容器 */}
      <div
        className="relative w-full h-full flex items-center justify-center overflow-hidden z-10"
        onClick={(e) => e.stopPropagation()}
      >
        <img
          src={src}
          alt={alt}
          className="max-w-none max-h-none select-none"
          style={imageStyle}
          onMouseDown={handleMouseDown}
          onDragStart={(e) => e.preventDefault()}
        />
      </div>

      {/* 快捷键提示 */}
      <div className="absolute bottom-4 left-4 z-20">
        <div className="bg-black-alpha-80 rounded-lg px-3 py-2 backdrop-blur-sm">
          <div className="text-white text-xs space-y-1">
            <div>ESC: {t("modal:imageModal.shortcuts.close")}</div>
            <div>+/-: {t("modal:imageModal.shortcuts.zoom")}</div>
            <div>R: {t("modal:imageModal.shortcuts.rotate")}</div>
            <div>0: {t("modal:imageModal.shortcuts.reset")}</div>
            <div>
              {t("modal:imageModal.shortcuts.drag")}:{" "}
              {t("modal:imageModal.shortcuts.move")}
            </div>
            <div>
              {t("modal:imageModal.shortcuts.wheel")}:{" "}
              {t("modal:imageModal.shortcuts.zoom")}
            </div>
          </div>
        </div>
      </div>

      {/* 图片信息 */}
      <div className="absolute bottom-4 right-4 z-20">
        <div className="bg-black-alpha-80 rounded-lg px-3 py-2 backdrop-blur-sm">
          <div className="text-white text-xs">
            <div>{alt}</div>
            <div>
              {t("modal:imageModal.info.scale")}:{" "}
              {Math.round(transform.scale * 100)}%
            </div>
            <div>
              {t("modal:imageModal.info.rotation")}: {transform.rotation}°
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};
