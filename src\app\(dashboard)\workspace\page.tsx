import dynamic from "next/dynamic";
import { PreloadWrapper } from "@/components/PreloadWrapper";

const HowToUse = dynamic(
  () => import("@/pages-components/work-space/how-to-use"),
  {
    loading: () => <div>Loading...</div>,
  }
);

const GenerationLog = dynamic(
  () => import("@/pages-components/work-space/generation-log"),
  {
    loading: () => <div>Loading...</div>,
  }
);
const Generate = dynamic(
  () => import("@/pages-components/work-space/generate"),
  {
    loading: () => <div>Loading...</div>,
  }
);

export default function WorkspacePage() {
  return (
    <PreloadWrapper
      routes={["/camera-roll", "/info-source", "/workspace/generating"]}
    >
      <div className="grid gap-4">
        <Generate />
        <HowToUse />
        {/* <WritingAssistant /> */}
        <GenerationLog />
      </div>
    </PreloadWrapper>
  );
}
