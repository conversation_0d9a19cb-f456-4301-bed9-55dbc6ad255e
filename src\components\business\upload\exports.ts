// 主要组件导出
export { default as UniversalUploadSystem } from "./index";
export { ImageUpload } from "./ImageUpload";
export { DocumentUpload } from "./DocumentUpload";

// 预览组件导出
export { ImageModal } from "./components/ImageModal";

// 类型导出
export type {
  UploadMode,
  UploadConfig,
  FileData,
  UploadCompleteInfo,
  UniversalUploadSystemProps,
} from "./types";

// 工具函数导出
export {
  mergeUploadConfig,
  getAllowedExtensions,
  getAllowedMimeTypes,
  isFileAllowed,
  PRESET_CONFIGS,
  DEFAULT_UPLOAD_CONFIG,
} from "./utils/uploadConfig";

export { validateFile } from "./utils/fileValidation";
export { formatFileSize, getFileCategory } from "./utils/fileUtils";

// 预设配置示例
export const UPLOAD_PRESETS = {
  // 头像上传
  avatar: {
    mode: "image" as const,
    maxSize: { image: 2 * 1024 * 1024 }, // 2MB
    multiple: false,
    showPreview: true,
    accept: { image: ["image/jpeg", "image/png"] },
  },

  // 文档上传
  documents: {
    mode: "document" as const,
    maxSize: { document: 10 * 1024 * 1024 }, // 10MB
    multiple: true,
    showPreview: false,
    accept: {
      document: [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ],
    },
  },

  // 图片库
  gallery: {
    mode: "image" as const,
    maxSize: { image: 5 * 1024 * 1024 }, // 5MB
    multiple: true,
    showPreview: true,
  },

  // 混合文件
  general: {
    mode: "mixed" as const,
    multiple: true,
    showPreview: true,
    showProgress: true,
  },
} as const;
