import { Card } from "@/components/business/card";
import { Skeleton } from "@/components/ui/skeleton";

const ContentPhotoStatusSkeleton = () => (
  <div className="grid grid-cols-2 max-md:grid-cols-1 gap-4">
    {/* StatCard 骨架屏 */}
    <Card>
      {/* 标题骨架屏 */}
      <Skeleton className="h-6 w-32 mb-4" />

      {/* 统计项骨架屏 */}
      <div className="grid gap-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index} className="bg-surface-light rounded-md py-3">
            {/* 数值骨架屏 */}
            <Skeleton className="h-7 w-8 mb-1" />
            {/* 标签骨架屏 */}
            <Skeleton className="h-4 w-20" />
          </Card>
        ))}
      </div>
    </Card>

    {/* PhotoUsageCard 骨架屏 */}
    <Card>
      {/* 标题骨架屏 */}
      <Skeleton className="h-6 w-28 mb-4" />

      {/* 使用进度骨架屏 */}
      <div className="mb-4">
        {/* 进度标签骨架屏 */}
        <div className="mb-4 flex justify-between items-center">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-12" />
        </div>
        {/* 进度条骨架屏 */}
        <Skeleton className="h-3 w-full rounded-full" />
      </div>

      {/* 按钮骨架屏 */}
      <Skeleton className="h-10 w-full rounded-md mt-4" />
    </Card>
  </div>
);

export default ContentPhotoStatusSkeleton;
