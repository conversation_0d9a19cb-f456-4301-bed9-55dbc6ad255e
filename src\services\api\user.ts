import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";
export interface IUser {
  id: string;
  username: string;
  email: string;
  avatar: string;
  phone: string;
  provider?: string; // 登录提供商 (google, phone)
}

export interface IGoogleLoginData {
  email: string;
  name: string;
  image?: string;
  provider: string;
}

export const signInApi = (data: {
  phone?: string;
  code?: string;
}): Promise<
  ApiResponse<{
    code: number;
    message: string;
    userId: string;
    token: string;
    user: IUser;
  }>
> => {
  return api.post(`/auth/register`, data, {
    errorConfig: {
      showToast: true,
      customMessage: "登录失败，请检查手机号和验证码",
      onError: (error) => {
        // 可以在这里添加特殊的错误处理逻辑
        console.error("Login error:", error);
      },
    },
  });
};

export const getCurrentUserApi = (): Promise<
  ApiResponse<{
    code: number;
    message: string;
    user: IUser;
  }>
> => {
  return api.postWithCache(`/auth/me`);
};

// 验证token
export const validateTokenApi = (): Promise<
  ApiResponse<{
    isValid: boolean;
  }>
> => {
  return api.post(`/auth/verify-token`);
};

// 谷歌登录
export const googleSignInApi = (
  data: IGoogleLoginData
): Promise<
  ApiResponse<{
    code: number;
    message: string;
    userId: string;
    token: string;
    user: IUser;
  }>
> => {
  return api.post(`/auth/google-login`, data, {
    errorConfig: {
      showToast: true,
      customMessage: "Google登录失败，请重试",
      onError: (error) => {
        console.error("Google login error:", error);
      },
    },
  });
};

// 同步NextAuth session到本地存储
export const syncSessionApi = (sessionData: {
  token: string;
  userId: string;
  user: IUser;
}): Promise<
  ApiResponse<{
    success: boolean;
  }>
> => {
  return api.post(`/auth/sync-session`, sessionData);
};
