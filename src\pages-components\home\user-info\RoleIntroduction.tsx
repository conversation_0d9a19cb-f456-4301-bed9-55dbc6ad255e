"use client";

import { DocumentTextIcon } from "@heroicons/react/24/solid";
import { Card } from "@/components/business/card";
import EmptyState, { EmptyType } from "@/components/business/empty";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import InfoCard from "./InfoCard";
import TopicTags from "./TopicTags";

interface RoleIntroductionProps {
  personality: string;
  description: string;
  topics: string[];
}

const RoleIntroduction = ({
  personality,
  description,
  topics,
}: RoleIntroductionProps) => {
  const { t } = useLanguage();

  return (
    <Card className="bg-surface-light persona-introduction">
      <div className="grid gap-8">
        <Text
          variant={TextVariant.H4}
          className="border-b border-border pb-2 relative flex gap-2 items-center"
        >
          <DocumentTextIcon className="size-4" />
          {t("home:userInfo.roleIntroduction")}
          <span className="absolute -bottom-0.5 left-0 bg-ring w-14 h-0.5"></span>
        </Text>

        <InfoCard
          title={t("home:userInfo.personality")}
          content={
            <Text
              variant={TextVariant.BODY_SMALL}
              className="text-text-secondary"
            >
              {personality || (
                <EmptyState
                  iconSize="sm"
                  compact
                  isDescription={false}
                  type={EmptyType.NO_DATA}
                />
              )}
            </Text>
          }
        />

        <InfoCard
          title={t("home:userInfo.detailIntroduction")}
          content={
            <Text
              variant={TextVariant.BODY_SMALL}
              className="text-text-secondary"
            >
              {description || (
                <EmptyState
                  iconSize="sm"
                  compact
                  isDescription={false}
                  type={EmptyType.NO_DATA}
                />
              )}
            </Text>
          }
        />

        <InfoCard
          title={t("home:userInfo.expertTopics")}
          content={
            topics.length > 0 ? (
              <TopicTags topics={topics} />
            ) : (
              <EmptyState
                iconSize="sm"
                compact
                isDescription={false}
                type={EmptyType.NO_DATA}
              />
            )
          }
        />
      </div>
    </Card>
  );
};

export default RoleIntroduction;
