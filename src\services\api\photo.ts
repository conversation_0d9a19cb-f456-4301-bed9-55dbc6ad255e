import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";
export interface IPhoto {
  createdAt: string;
  fileName: string;
  id: string;
  imageUrl: string;
  isUse: boolean;
  personaId: string;
  s3Key: string;
  summary: string;
  updatedAt: string;
  userId: string;
}
// 上传照片photo/upload
export const uploadPhotoApi = async (data: {
  personaId: string;
  photos: {
    fileName: string;
    s3Key: string;
  }[];
}): Promise<ApiResponse<{ fileKey: string }>> => {
  return await api.post(`/photo/upload`, data);
};

// list
export const photoListApi = async (data: {
  personaId: string;
}): Promise<
  ApiResponse<{
    pagination: { count: number; hasMore: boolean };
    photos: IPhoto[];
  }>
> => {
  return await api.post(`/photo/list`, data);
};

// photo/statistics
export const photoStatisticsApi = async (data: {
  personaId: string;
}): Promise<
  ApiResponse<{
    summary: {
      total: number;
      used: number;
      unused: number;
    };
    types: {
      all: { type: string; count: number }[];
    };
  }>
> => {
  return await api.postWithCache(`/photo/statistics`, data);
};

// photo/filter
export const photoFilterApi = async (data: {
  personaId: string;
  type: string;
}): Promise<
  ApiResponse<{
    pagination: { count: number; hasMore: boolean };
    photos: IPhoto[];
  }>
> => {
  return await api.post(`/photo/filter`, data);
};

///photo/delete
export const photoDeleteApi = async (data: {
  photoId: string;
}): Promise<ApiResponse<{ success: boolean }>> => {
  return await api.post(`/photo/delete`, data);
};
