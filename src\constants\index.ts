// 应用配置
export const APP_CONFIG = {
  name: "XHS",
  version: "0.1.0",
  description: "AI-powered content generation platform inspired by <PERSON><PERSON><PERSON>",
  author: "XHS Team",
  repository: "https://github.com/your-org/xhs",
} as const;

// 国际化命名空间
export const I18N_NAMESPACES = [
  "common",
  "theme",
  "language",
  "navigation",
  "pages",
  "components",
  "effects",
  "errors",
  "validation",
  "message",
  /////
  "nav",
  "route",
  "button",
  "modal",
  "home",
  "informationSource",
] as const;

// 路由配置
export const ROUTES = {
  HOME: "/",
  THEME_DEMO: "/theme-demo",
  NEXTUI_DEMO: "/nextui-demo",
  EMPTY_DEMO: "/empty-demo",
  ABOUT: "/about",
  CONTACT: "/contact",
} as const;

// API 配置
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || "/api",
  timeout: 10000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
  USER_PREFERENCES: "user-preferences",
  RECENT_SEARCHES: "recent-searches",
  DRAFT_CONTENT: "draft-content",
} as const;

// 响应式断点
export const BREAKPOINTS = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px",
} as const;

// 动画配置
export const ANIMATION_CONFIG = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
  easing: {
    ease: "ease",
    easeIn: "ease-in",
    easeOut: "ease-out",
    easeInOut: "ease-in-out",
  },
} as const;

// 颜色配置
export const COLOR_CONFIG = {
  primary: {
    50: "#eff6ff",
    100: "#dbeafe",
    200: "#bfdbfe",
    300: "#93c5fd",
    400: "#60a5fa",
    500: "#3b82f6",
    600: "#2563eb",
    700: "#1d4ed8",
    800: "#1e40af",
    900: "#1e3a8a",
  },
  secondary: {
    50: "#f0fdfa",
    100: "#ccfbf1",
    200: "#99f6e4",
    300: "#5eead4",
    400: "#2dd4bf",
    500: "#14b8a6",
    600: "#0d9488",
    700: "#0f766e",
    800: "#115e59",
    900: "#134e4a",
  },
} as const;

// 组件默认配置
export const COMPONENT_DEFAULTS = {
  button: {
    variant: "primary" as const,
    size: "md" as const,
  },
  text: {
    variant: "body-medium" as const,
  },
  card: {
    variant: "default" as const,
    padding: "md" as const,
  },
  modal: {
    size: "md" as const,
    closable: true,
    maskClosable: true,
  },
} as const;

// 验证规则
export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^https?:\/\/.+/,
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
} as const;

// 文件上传配置
export const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedImageTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  allowedDocumentTypes: ["application/pdf", "text/plain", "application/msword"],
  maxFiles: 5,
} as const;

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],
  maxPageSize: 100,
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxAge: {
    short: 1 * 60 * 1000, // 1分钟
    medium: 5 * 60 * 1000, // 5分钟
    long: 30 * 60 * 1000, // 30分钟
    persistent: 24 * 60 * 60 * 1000, // 24小时
  },
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "网络连接错误，请检查网络设置",
  SERVER_ERROR: "服务器错误，请稍后重试",
  UNAUTHORIZED: "未授权访问，请先登录",
  FORBIDDEN: "权限不足，无法访问该资源",
  NOT_FOUND: "请求的资源不存在",
  VALIDATION_ERROR: "输入数据验证失败",
  TIMEOUT_ERROR: "请求超时，请稍后重试",
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: "保存成功",
  UPDATE_SUCCESS: "更新成功",
  DELETE_SUCCESS: "删除成功",
  UPLOAD_SUCCESS: "上传成功",
  COPY_SUCCESS: "复制成功",
} as const;

// 开发环境配置
export const DEV_CONFIG = {
  enableDebug: process.env.NODE_ENV === "development",
  logLevel: process.env.NEXT_PUBLIC_LOG_LEVEL || "info",
} as const;
