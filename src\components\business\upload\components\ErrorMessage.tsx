import { AlertCircle } from "lucide-react";
import type { ErrorMessageProps } from "../types";

export const ErrorMessage = ({ errors }: ErrorMessageProps) => {
  return (
    <div className="mt-3 p-3 bg-error-alpha-10 border border-error rounded-lg">
      <div className="flex items-center space-x-2">
        <AlertCircle className="w-4 h-4 text-error" />
        <span className="text-sm text-error">{errors.join(", ")}</span>
      </div>
    </div>
  );
};
