import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  uploadPhotoApi,
  photoListApi,
  IPhoto,
  photoStatisticsApi,
  photoFilterApi,
  photoDeleteApi,
} from "@/services/api/photo";
interface PhotoStore {
  photoList: IPhoto[];
  analysis: Record<string, any>;
  isLoading: boolean;
  isUploading: boolean;
  isFiltering: boolean;
  uploadPhoto: (
    personaId: string,
    photos: { fileName: string; s3Key: string }[]
  ) => Promise<void>;
  getPhotoList: (personaId: string, isPolling?: boolean) => Promise<void>;
  pagination: { count: number; hasMore: boolean };
  statistics: { type: string; count: number; key?: string }[];
  getPhotoStatistics: (personaId: string) => Promise<void>;
  currentType: string;
  setCurrentType: (type: string) => void;
  photoFilter: (personaId: string, type: string) => Promise<void>;
  isPolling: boolean;
  deletePhoto: (photoId: string, personaId: string) => Promise<void>;
}
const usePhotoStore = create<PhotoStore>()(
  immer(
    (set, get): PhotoStore => ({
      photoList: [],
      analysis: {},
      statistics: [],
      currentType: "total",
      pagination: { count: 0, hasMore: false },
      isLoading: false,
      isUploading: false,
      isFiltering: false,
      isPolling: false,
      setCurrentType: (type: string) => {
        set({
          currentType: type,
        });
      },
      uploadPhoto: async (
        personaId: string,
        photos: { fileName: string; s3Key: string }[]
      ) => {
        try {
          set({ isUploading: true });
          await uploadPhotoApi({
            personaId,
            photos,
          });
          await get().getPhotoList(personaId);
        } finally {
          set({ isUploading: false });
        }
      },
      getPhotoList: async (personaId: string, isUploading?: boolean) => {
        try {
          set({ isLoading: !isUploading });
          const res = await photoListApi({
            personaId,
          });
          if (res.success) {
            set({
              photoList: res.data?.photos || [],
              pagination: res.data?.pagination,
              isPolling: res.data?.photos.some(
                (photo) => !photo.summary.includes("content")
              ),
            });
            await get().getPhotoStatistics(personaId);
          }
        } finally {
          set({ isLoading: false });
        }
      },
      getPhotoStatistics: async (personaId: string) => {
        get().setCurrentType("total");
        const res = await photoStatisticsApi({
          personaId,
        });
        if (res.success && res.data) {
          const { total, used, unused } = res.data.summary;
          const { all } = res.data.types;
          set({
            statistics: [
              { type: "total", count: total, key: "total" },
              { type: "used", count: used, key: "used" },
              { type: "unused", count: unused, key: "unused" },
              ...all,
            ],
          });
        }
      },
      photoFilter: async (personaId: string, type: string) => {
        try {
          set({ isFiltering: true });
          const res = await photoFilterApi({
            personaId,
            type,
          });
          if (res.success) {
            set({
              photoList: res.data?.photos || [],
              pagination: res.data?.pagination,
            });
          }
        } finally {
          set({ isFiltering: false });
        }
      },
      deletePhoto: async (photoId: string, personaId: string) => {
        await photoDeleteApi({ photoId });
        await get().getPhotoList(personaId);
      },
    })
  )
);

export default usePhotoStore;
