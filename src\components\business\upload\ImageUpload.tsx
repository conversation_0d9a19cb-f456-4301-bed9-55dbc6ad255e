import React from "react";
import type { UniversalUploadSystemProps } from "./types";
import UniversalUploadSystem from "./index";

interface ImageUploadProps extends Omit<UniversalUploadSystemProps, "config"> {
  maxSize?: number; // MB
  multiple?: boolean;
  maxFiles?: number; // 最大文件数量
  showPreview?: boolean;
}

/**
 * 专用图片上传组件
 * 只允许上传图片文件
 */
export const ImageUpload = ({
  maxSize = 5,
  multiple = true,
  maxFiles = 5,
  showPreview = true,
  ...props
}: ImageUploadProps) => {
  const config = {
    mode: "image" as const,
    maxSize: {
      image: maxSize * 1024 * 1024, // 转换为字节
    },
    multiple,
    maxFiles,
    showPreview,
    showProgress: true,
  };

  return <UniversalUploadSystem {...props} config={config} />;
};

export default ImageUpload;
