"use client";

import { usePathname } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useCache } from "@/hooks/useCache";

interface PageCacheConfig {
  ttl?: number;
  autoSave?: boolean;
  saveInterval?: number;
  includeUrl?: boolean;
}

interface PageData {
  formData?: Record<string, any>;
  scrollPosition?: number;
  selectedItems?: any[];
  filters?: Record<string, any>;
  searchQuery?: string;
  timestamp?: number;
}

export const usePageCache = (pageKey?: string, config?: PageCacheConfig) => {
  const pathname = usePathname();
  const {
    ttl = 30 * 60 * 1000, // 30分钟默认缓存
    autoSave = true,
    saveInterval = 5000, // 5秒自动保存间隔
    includeUrl = true,
  } = config || {};

  // 生成缓存键
  const cacheKey =
    pageKey || (includeUrl ? `page_${pathname}` : "page_default");

  const {
    data: cachedData,
    updateCache,
    clearCache,
  } = useCache<PageData>({
    key: cacheKey,
    ttl,
  });

  const [pageData, setPageData] = useState<PageData>(cachedData || {});
  const [isDirty, setIsDirty] = useState(false);

  // 更新页面数据
  const updatePageData = useCallback((updates: Partial<PageData>) => {
    setPageData((prev) => {
      const newData = { ...prev, ...updates, timestamp: Date.now() };
      setIsDirty(true);
      return newData;
    });
  }, []);

  // 保存到缓存
  const saveToCache = useCallback(() => {
    if (isDirty) {
      updateCache(pageData);
      setIsDirty(false);
    }
  }, [pageData, isDirty, updateCache]);

  // 从缓存恢复
  const restoreFromCache = useCallback(() => {
    if (cachedData) {
      setPageData(cachedData);
      setIsDirty(false);
    }
  }, [cachedData]);

  // 清除页面缓存
  const clearPageCache = useCallback(() => {
    clearCache();
    setPageData({});
    setIsDirty(false);
  }, [clearCache]);

  // 表单数据管理
  const setFormData = useCallback(
    (formData: Record<string, any>) => {
      updatePageData({ formData });
    },
    [updatePageData]
  );

  const getFormData = useCallback(() => {
    return pageData.formData || {};
  }, [pageData.formData]);

  // 滚动位置管理
  const saveScrollPosition = useCallback(() => {
    const scrollPosition = window.scrollY;
    updatePageData({ scrollPosition });
  }, [updatePageData]);

  const restoreScrollPosition = useCallback(() => {
    if (pageData.scrollPosition !== undefined) {
      window.scrollTo(0, pageData.scrollPosition);
    }
  }, [pageData.scrollPosition]);

  // 选中项管理
  const setSelectedItems = useCallback(
    (items: any[]) => {
      updatePageData({ selectedItems: items });
    },
    [updatePageData]
  );

  const getSelectedItems = useCallback(() => {
    return pageData.selectedItems || [];
  }, [pageData.selectedItems]);

  // 筛选器管理
  const setFilters = useCallback(
    (filters: Record<string, any>) => {
      updatePageData({ filters });
    },
    [updatePageData]
  );

  const getFilters = useCallback(() => {
    return pageData.filters || {};
  }, [pageData.filters]);

  // 搜索查询管理
  const setSearchQuery = useCallback(
    (query: string) => {
      updatePageData({ searchQuery: query });
    },
    [updatePageData]
  );

  const getSearchQuery = useCallback(() => {
    return pageData.searchQuery || "";
  }, [pageData.searchQuery]);

  // 自动保存
  useEffect(() => {
    if (!autoSave) {
      return;
    }

    const interval = setInterval(() => {
      saveToCache();
    }, saveInterval);

    return () => clearInterval(interval);
  }, [autoSave, saveInterval, saveToCache]);

  // 页面卸载时保存
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveToCache();
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [saveToCache]);

  // 初始化时恢复数据
  useEffect(() => {
    if (cachedData && Object.keys(pageData).length === 0) {
      restoreFromCache();
    }
  }, [cachedData, pageData, restoreFromCache]);

  return {
    // 数据状态
    pageData,
    isDirty,
    hasCache: !!cachedData,
    cacheKey,

    // 通用操作
    updatePageData,
    saveToCache,
    restoreFromCache,
    clearPageCache,

    // 表单数据
    setFormData,
    getFormData,

    // 滚动位置
    saveScrollPosition,
    restoreScrollPosition,

    // 选中项
    setSelectedItems,
    getSelectedItems,

    // 筛选器
    setFilters,
    getFilters,

    // 搜索查询
    setSearchQuery,
    getSearchQuery,
  };
};

// 创建特定用途的页面缓存 Hook
export const useFormCache = (formKey: string) => {
  const { setFormData, getFormData, saveToCache, clearPageCache } =
    usePageCache(
      `form_${formKey}`,
      { ttl: 60 * 60 * 1000 } // 1小时
    );

  return {
    saveForm: setFormData,
    loadForm: getFormData,
    saveToCache,
    clearForm: clearPageCache,
  };
};

export const useListCache = (listKey: string) => {
  const {
    setSelectedItems,
    getSelectedItems,
    setFilters,
    getFilters,
    setSearchQuery,
    getSearchQuery,
    saveScrollPosition,
    restoreScrollPosition,
    clearPageCache,
  } = usePageCache(`list_${listKey}`, { ttl: 15 * 60 * 1000 }); // 15分钟

  return {
    // 选中项
    selectedItems: getSelectedItems(),
    setSelectedItems,

    // 筛选器
    filters: getFilters(),
    setFilters,

    // 搜索
    searchQuery: getSearchQuery(),
    setSearchQuery,

    // 滚动位置
    saveScrollPosition,
    restoreScrollPosition,

    // 清除
    clearListCache: clearPageCache,
  };
};
