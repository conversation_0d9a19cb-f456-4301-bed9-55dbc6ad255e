"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Card } from "@/components/business/card";
import { Select } from "@/components/business/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { RequiredLabel } from "@/components/ui/RequiredLabel";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import { IConfig } from "@/services/api/setting";
import useRoleStore from "@/store/persona";
import useSettingStore from "@/store/setting";

// AI 设置表单数据接

const AiSettings = () => {
  const { roleList, currentRole } = useRoleStore();
  const { t } = useLanguage();
  const { createAiSetting, getAiSetting, aiSetting } = useSettingStore();

  // 初始化表单
  const form = useForm<IConfig>({
    defaultValues: {
      writingStyle: "warmAndCute",
      contentLength: "short",
      writingFields: [],
      // ...aiSetting,
    },
    mode: "onChange",
  });

  // 表单提交处理
  const handleFormSubmit = async (data: IConfig) => {
    if (roleList.length === 0) {
      toast.error(t("components:empty.description.noRoles"));
      return;
    }

    try {
      await createAiSetting({
        personaId: currentRole.personaId,
        ...data,
      }); // 保存 AI 设置到后端
      toast.success(t("setting:ai.saveSuccess"));
    } catch (error) {
      console.error("保存 AI 设置失败:", error);
      toast.error(t("setting:ai.saveError"));
    }
  };

  // 写作领域选项
  const writingFieldOptions = [
    {
      label: t("setting:ai.form.writingFieldOptions.dailyLife"),
      value: "dailyLife",
    },
    {
      label: t("setting:ai.form.writingFieldOptions.beauty"),
      value: "beauty",
    },
    {
      label: t("setting:ai.form.writingFieldOptions.food"),
      value: "food",
    },
    {
      label: t("setting:ai.form.writingFieldOptions.travel"),
      value: "travel",
    },
  ];
  useEffect(() => {
    if (currentRole && currentRole.personaId) {
      getAiSetting(currentRole.personaId);
    }
  }, [currentRole, getAiSetting]);

  useEffect(() => {
    if (aiSetting && aiSetting.personaId === currentRole.personaId) {
      form.reset(aiSetting);
    }
  }, [aiSetting, currentRole, form]);

  return (
    <Card>
      <Text
        variant={TextVariant.H4}
        className="mb-4 flex items-center justify-between"
      >
        {t("setting:ai.title")}
      </Text>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleFormSubmit)}
          className="grid gap-4"
        >
          {/* 写作风格 - 必填 */}
          <FormField
            control={form.control}
            name="writingStyle"
            rules={{
              required: t("setting:ai.validation.writingStyleRequired"),
            }}
            render={({ field }) => (
              <FormItem>
                <RequiredLabel required>
                  {t("setting:ai.form.writingStyle")}
                </RequiredLabel>
                <FormControl>
                  <Select
                    className="w-full"
                    value={field.value}
                    onValueChange={field.onChange}
                    options={[
                      {
                        name: t(
                          "setting:ai.form.writingStyleOptions.warmAndCute"
                        ),
                        value: "warmAndCute",
                      },
                      {
                        name: t(
                          "setting:ai.form.writingStyleOptions.professionalAndStrict"
                        ),
                        value: "professionalAndStrict",
                      },
                      {
                        name: t(
                          "setting:ai.form.writingStyleOptions.livelyAndFun"
                        ),
                        value: "livelyAndFun",
                      },
                      {
                        name: t(
                          "setting:ai.form.writingStyleOptions.literaryAndFresh"
                        ),
                        value: "literaryAndFresh",
                      },
                    ]}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* 内容长度 - 必填 */}
          <FormField
            control={form.control}
            name="contentLength"
            rules={{
              required: t("setting:ai.validation.contentLengthRequired"),
            }}
            render={({ field }) => (
              <FormItem>
                <RequiredLabel required>
                  {t("setting:ai.form.contentLength")}
                </RequiredLabel>
                <FormControl>
                  <Select
                    className="w-full"
                    value={field.value}
                    onValueChange={field.onChange}
                    options={[
                      {
                        name: t("setting:ai.form.contentLengthOptions.short"),
                        value: "short",
                      },
                      {
                        name: t("setting:ai.form.contentLengthOptions.medium"),
                        value: "medium",
                      },
                      {
                        name: t("setting:ai.form.contentLengthOptions.long"),
                        value: "long",
                      },
                    ]}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* 写作领域 - 可选 */}
          <FormField
            control={form.control}
            name="writingFields"
            render={({ field }) => (
              <FormItem>
                <Label>{t("setting:ai.form.writingField")}</Label>
                <FormControl>
                  <div className="flex gap-2 items-center">
                    {writingFieldOptions.map((option) => (
                      <Card
                        key={option.value}
                        className={`hover:border-ring cursor-pointer transition-colors ${
                          field.value?.includes(option.value)
                            ? "border-primary bg-primary-alpha-10"
                            : "border-border"
                        }`}
                      >
                        <Label
                          htmlFor={`checkbox-${option.value}`}
                          className="flex items-center select-none cursor-pointer w-full"
                        >
                          <Checkbox
                            id={`checkbox-${option.value}`}
                            checked={field.value?.includes(option.value)}
                            className="bg-white-alpha-20"
                            onCheckedChange={(checked) => {
                              const currentValues = field.value || [];

                              // 使用 setTimeout 来避免同步更新导致的无限循环
                              setTimeout(() => {
                                if (checked) {
                                  // 添加
                                  field.onChange([
                                    ...currentValues,
                                    option.value,
                                  ]);
                                } else {
                                  // 移除
                                  field.onChange(
                                    currentValues.filter(
                                      (value) => value !== option.value
                                    )
                                  );
                                }
                              }, 0);
                            }}
                          />
                          <Text
                            variant={TextVariant.BODY_SMALL}
                            className="ml-2"
                          >
                            {option.label}
                          </Text>
                        </Label>
                      </Card>
                    ))}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 提交按钮 */}
          <div className="flex justify-end gap-4 mt-6">
            <Button
              type="button"
              variant="secondary"
              onClick={() =>
                form.reset({
                  writingStyle: "warmAndCute",
                  contentLength: "short",
                  writingFields: [],
                })
              }
            >
              {t("button:reset")}
            </Button>
            <Button type="submit" className="text-text-primary">
              {t("button:save")}
            </Button>
          </div>
        </form>
      </Form>
    </Card>
  );
};

export default AiSettings;
