import React, { useMemo } from "react";

export interface ProfileIconProps {
  className?: string;
  size?: number;
  seed?: string; // 用于生成一致的随机结果
}

export const ProfileIcon: React.FC<ProfileIconProps> = ({
  className,
  size = 32,
  seed = Math.random().toString(),
}) => {
  // 随机生成配置
  const randomConfig = useMemo(() => {
    const personas: Array<"lifestyle" | "fashion" | "food"> = [
      "lifestyle",
      "fashion",
      "food",
    ];
    const genders: Array<"Male" | "Female"> = ["Male", "Female"];

    // 使用 seed 生成一致的随机数
    const hash = seed.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);

    const randomPersona = personas[Math.abs(hash) % personas.length];
    const randomGender = genders[Math.abs(hash * 2) % genders.length];

    return {
      persona: randomPersona,
      gender: randomGender,
    };
  }, [seed]);

  const colors = {
    lifestyle: {
      skin: "#FDBCB4",
      skinStroke: "#E6A8A2",
      hair: "#FF69B4",
      hairHighlight: "#FF1493",
      accessories: "#FFD700",
    },
    fashion: {
      skin: "#F4C2A1",
      skinStroke: "#E6A576",
      hair: "#8B4513",
      hairHighlight: "#A0522D",
      accessories: "#E6E6FA",
    },
    food: {
      skin: "#DEB887",
      skinStroke: "#CD853F",
      hair: "#2F4F4F",
      hairHighlight: "#708090",
      accessories: "#32CD32",
    },
  };

  const generateCustomColors = (key: string) => {
    const skinTones = [
      "#FDBCB4",
      "#F4C2A1",
      "#DEB887",
      "#E6C2A0",
      "#FFDBAC",
      "#F5DEB3",
      "#D2B48C",
      "#CD853F",
      "#F0E68C",
      "#FFE4B5",
      "#FFEFD5",
      "#FFDAB9",
      "#EEE8AA",
      "#F5F5DC",
      "#FDF5E6",
    ];
    const hairColors = [
      "#FF69B4",
      "#8B4513",
      "#2F4F4F",
      "#DAA520",
      "#8B008B",
      "#FF6347",
      "#4B0082",
      "#800080",
      "#9932CC",
      "#BA55D3",
      "#DDA0DD",
      "#E6E6FA",
      "#000000",
      "#2F2F2F",
      "#654321",
      "#8B4513",
      "#A0522D",
      "#D2691E",
      "#CD853F",
      "#F4A460",
      "#DEB887",
      "#D2B48C",
      "#BC8F8F",
      "#F5DEB3",
      "#FFD700",
      "#FFA500",
      "#FF8C00",
      "#FF7F50",
      "#FF6347",
      "#FF4500",
      "#DC143C",
      "#B22222",
      "#8B0000",
      "#800000",
      "#556B2F",
      "#6B8E23",
      "#808000",
      "#9ACD32",
      "#ADFF2F",
      "#7FFF00",
      "#32CD32",
      "#00FF00",
    ];
    const accessories = [
      "#FFD700",
      "#E6E6FA",
      "#32CD32",
      "#FF1493",
      "#00CED1",
      "#FF69B4",
      "#FF4500",
      "#FF6347",
      "#FF7F50",
      "#FFA500",
      "#FFB6C1",
      "#FFC0CB",
      "#FFCCCB",
      "#FFD700",
      "#FFFF00",
      "#ADFF2F",
      "#7FFF00",
      "#32CD32",
      "#00FF7F",
      "#00FFFF",
      "#00BFFF",
      "#0080FF",
      "#4169E1",
      "#6495ED",
      "#87CEEB",
      "#87CEFA",
      "#B0C4DE",
      "#B0E0E6",
      "#ADD8E6",
      "#E0FFFF",
      "#F0FFFF",
      "#F5FFFA",
      "#F0FFF0",
      "#FFFACD",
      "#FFFFE0",
      "#FFFFF0",
      "#9370DB",
      "#8A2BE2",
      "#9400D3",
      "#9932CC",
      "#BA55D3",
      "#DA70D6",
      "#EE82EE",
      "#DDA0DD",
      "#D8BFD8",
      "#THISTLE",
      "#PLUM",
      "#VIOLET",
    ];

    const hash = key.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);

    const skinIndex = Math.abs(hash) % skinTones.length;
    const hairIndex = Math.abs(hash * 2) % hairColors.length;
    const accIndex = Math.abs(hash * 3) % accessories.length;

    return {
      skin: skinTones[skinIndex],
      skinStroke: skinTones[skinIndex].replace("#", "#").slice(0, 7) + "80",
      hair: hairColors[hairIndex],
      hairHighlight: hairColors[hairIndex] + "80",
      accessories: accessories[accIndex],
    };
  };

  // 生成随机特征
  const generateRandomFeatures = (key: string) => {
    const eyeColors = [
      "#333",
      "#654321",
      "#8B4513",
      "#2F4F4F",
      "#556B2F",
      "#4169E1",
      "#32CD32",
    ];
    const mouthColors = [
      "#DC143C",
      "#B22222",
      "#FF69B4",
      "#FF1493",
      "#FF6347",
      "#CD5C5C",
    ];

    const hash = key.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);

    const eyeIndex = Math.abs(hash * 4) % eyeColors.length;
    const mouthIndex = Math.abs(hash * 5) % mouthColors.length;
    const hasFreckles = Math.abs(hash * 6) % 3 === 0; // 33% 概率有雀斑
    const hasDimples = Math.abs(hash * 7) % 4 === 0; // 25% 概率有酒窝

    return {
      eyeColor: eyeColors[eyeIndex],
      mouthColor: mouthColors[mouthIndex],
      hasFreckles,
      hasDimples,
    };
  };

  const personaColors =
    colors[randomConfig.persona] || generateCustomColors(randomConfig.persona);
  const randomFeatures = generateRandomFeatures(seed);
  const isMale = randomConfig.gender === "Male";

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      fill="none"
      className={className}
    >
      {/* Face */}
      <circle
        cx="50"
        cy="45"
        r="35"
        fill={personaColors.skin}
        stroke={personaColors.skinStroke}
        strokeWidth="2"
      />

      {/* Hair */}
      {isMale ? (
        <>
          {/* Male hair */}
          <ellipse cx="50" cy="20" rx="32" ry="20" fill={personaColors.hair} />
          <ellipse
            cx="45"
            cy="18"
            rx="6"
            ry="12"
            fill={personaColors.hairHighlight}
            opacity="0.5"
          />
        </>
      ) : (
        <>
          {/* Female hair */}
          <ellipse cx="50" cy="25" rx="38" ry="25" fill={personaColors.hair} />
          <ellipse
            cx="45"
            cy="20"
            rx="8"
            ry="15"
            fill={personaColors.hairHighlight}
            opacity="0.5"
          />
        </>
      )}

      {/* Eyes */}
      <circle cx="42" cy="40" r="3" fill={randomFeatures.eyeColor} />
      <circle cx="58" cy="40" r="3" fill={randomFeatures.eyeColor} />
      <circle cx="43" cy="39" r="1" fill="#FFF" />
      <circle cx="59" cy="39" r="1" fill="#FFF" />

      {/* Eyebrows */}
      <path d="M42 35 L42 32" stroke="#333" strokeWidth="1" />
      <path d="M58 35 L58 32" stroke="#333" strokeWidth="1" />

      {/* Nose */}
      <ellipse cx="50" cy="48" rx="2" ry="3" fill="#F4A460" />

      {/* Mouth */}
      <path
        d="M45 55 Q50 60 55 55"
        stroke={randomFeatures.mouthColor}
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
      />

      {/* Freckles */}
      {randomFeatures.hasFreckles && (
        <>
          <circle cx="38" cy="50" r="0.5" fill="#D2691E" opacity="0.6" />
          <circle cx="62" cy="48" r="0.5" fill="#D2691E" opacity="0.6" />
          <circle cx="46" cy="52" r="0.5" fill="#D2691E" opacity="0.6" />
          <circle cx="54" cy="51" r="0.5" fill="#D2691E" opacity="0.6" />
        </>
      )}

      {/* Dimples */}
      {randomFeatures.hasDimples && (
        <>
          <ellipse
            cx="40"
            cy="58"
            rx="1"
            ry="0.5"
            fill={personaColors.skinStroke}
            opacity="0.3"
          />
          <ellipse
            cx="60"
            cy="58"
            rx="1"
            ry="0.5"
            fill={personaColors.skinStroke}
            opacity="0.3"
          />
        </>
      )}

      {/* Accessories/Earrings */}
      {!isMale && (
        <>
          <circle cx="28" cy="45" r="2" fill={personaColors.accessories} />
          <circle cx="72" cy="45" r="2" fill={personaColors.accessories} />
        </>
      )}

      {/* Unique persona features */}
      {randomConfig.persona === "lifestyle" && (
        <>
          {/* Lifestyle: Flower accessory */}
          <circle cx="65" cy="25" r="3" fill="#FF69B4" />
          <circle cx="65" cy="25" r="1.5" fill="#FFD700" />
        </>
      )}

      {randomConfig.persona === "fashion" && (
        <>
          {/* Fashion: Stylish glasses */}
          <ellipse
            cx="42"
            cy="40"
            rx="6"
            ry="4"
            fill="none"
            stroke="#333"
            strokeWidth="1"
          />
          <ellipse
            cx="58"
            cy="40"
            rx="6"
            ry="4"
            fill="none"
            stroke="#333"
            strokeWidth="1"
          />
          <path d="M48 40 L52 40" stroke="#333" strokeWidth="1" />
        </>
      )}

      {randomConfig.persona === "food" && (
        <>
          {/* Food: Chef hat */}
          <ellipse
            cx="50"
            cy="15"
            rx="25"
            ry="8"
            fill="#FFF"
            stroke="#DDD"
            strokeWidth="1"
          />
          <rect
            x="40"
            y="15"
            width="20"
            height="8"
            fill="#FFF"
            stroke="#DDD"
            strokeWidth="1"
          />
        </>
      )}
    </svg>
  );
};

export default ProfileIcon;
