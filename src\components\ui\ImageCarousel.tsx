"use client";

import {
  CameraIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/solid";
import { useImageCarousel } from "@/hooks/useImageCarousel";
import { cn } from "@/lib/utils";

interface Photo {
  imageUrl: string;
  id: string;
}

interface ImageCarouselProps {
  /** 图片数组 */
  photos: Photo[];
  /** 容器的CSS类名 */
  className?: string;
  /** 图片容器的宽高比 */
  aspectRatio?: string;
  /** 是否显示导航箭头 */
  showArrows?: boolean;
  /** 是否显示页码指示器 */
  showPageIndicator?: boolean;
  /** 是否显示轮播指示点 */
  showDots?: boolean;
  /** 是否启用键盘导航 */
  enableKeyboardNavigation?: boolean;
  /** 是否启用图片预加载 */
  enablePreloading?: boolean;
  /** 占位符图标大小 */
  placeholderIconSize?: string;
  /** 自定义占位符内容 */
  placeholder?: React.ReactNode;
  /** 图片加载失败时的占位符 */
  errorPlaceholder?: React.ReactNode;
}

/**
 * 图片轮播组件
 *
 * 功能特性：
 * - 支持多图片轮播展示
 * - 键盘导航支持（左右箭头键）
 * - 图片预加载和错误处理
 * - 响应式设计
 * - 无障碍访问支持
 * - 高度可配置
 */
export const ImageCarousel = ({
  photos,
  className,
  aspectRatio = "aspect-[4/3]",
  showArrows = true,
  showPageIndicator = true,
  showDots = true,
  enableKeyboardNavigation = true,
  enablePreloading = true,
  placeholderIconSize = "w-16 h-16",
  placeholder,
  errorPlaceholder,
}: ImageCarouselProps) => {
  const {
    currentImageIndex,
    hasMultipleImages,
    isCurrentImageError,
    goToPrevImage,
    goToNextImage,
    goToImage,
    handleImageError,
  } = useImageCarousel({
    photos,
    enableKeyboardNavigation,
    enablePreloading,
  });

  const defaultPlaceholder = (
    <CameraIcon className={cn(placeholderIconSize, "text-gray-600")} />
  );

  const defaultErrorPlaceholder = (
    <div className="text-center text-text-tertiary">
      <CameraIcon className="w-12 h-12 mx-auto mb-2" />
      <p className="text-sm">图片加载失败</p>
    </div>
  );

  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          "relative bg-gray-800 rounded-lg flex items-center justify-center group overflow-hidden",
          aspectRatio
        )}
      >
        {photos.length > 0 ? (
          <>
            {/* 图片 */}
            {!isCurrentImageError ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                key={currentImageIndex}
                src={photos[currentImageIndex].imageUrl}
                alt={`Generated content ${currentImageIndex + 1}`}
                className="absolute top-0 left-0 w-full h-full object-cover rounded-lg transition-opacity duration-300"
                onError={() => handleImageError(currentImageIndex)}
                loading="lazy"
              />
            ) : (
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-surface-light rounded-lg">
                {errorPlaceholder || defaultErrorPlaceholder}
              </div>
            )}

            {/* 页码指示器 */}
            {hasMultipleImages && showPageIndicator && (
              <div className="absolute top-3 right-3 bg-black-alpha-80 text-white text-xs px-2 py-1 rounded-full">
                {currentImageIndex + 1}/{photos.length}
              </div>
            )}

            {/* 左右箭头 */}
            {hasMultipleImages && showArrows && (
              <>
                <button
                  onClick={goToPrevImage}
                  className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black-alpha-80 hover:bg-black rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  aria-label="上一张图片"
                >
                  <ChevronLeftIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={goToNextImage}
                  className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black-alpha-80 hover:bg-black rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  aria-label="下一张图片"
                >
                  <ChevronRightIcon className="w-4 h-4" />
                </button>
              </>
            )}
          </>
        ) : (
          /* 图片占位符 */
          placeholder || defaultPlaceholder
        )}
      </div>

      {/* 轮播指示点 */}
      {hasMultipleImages && showDots && (
        <div className="flex justify-center gap-1 mt-3">
          {photos.map((_, index) => (
            <button
              key={index}
              onClick={() => goToImage(index)}
              className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                index === currentImageIndex
                  ? "bg-white"
                  : "bg-gray-600 hover:bg-gray-500"
              }`}
              aria-label={`查看第 ${index + 1} 张图片`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
