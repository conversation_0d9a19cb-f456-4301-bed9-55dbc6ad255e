"use client";

import { Card } from "@/components/business/card";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import { GeneratedSummaryProps } from "../types";

/**
 * 生成结果摘要组件
 * 显示生成内容的数量和平台信息
 */
const GeneratedSummary = ({
  contentCount,
  currentLanguage,
}: GeneratedSummaryProps) => {
  const { t } = useLanguage();

  // 根据语言确定平台名称
  const getPlatformName = () => {
    return t(
      currentLanguage === "zh-CN"
        ? "workspace:generated.xhs"
        : "workspace:generated.twitter"
    );
  };

  return (
    <Card className="border-primary-alpha-20 bg-primary-alpha-10">
      <div>
        <Text variant={TextVariant.H3} className="mb-4 text-primary">
          {t("workspace:generated.title", {
            num: contentCount,
            platform: getPlatformName(),
          })}
        </Text>
        <Text variant={TextVariant.BODY_MEDIUM} className="text-text-secondary">
          {t("workspace:generated.tip", {
            platform: getPlatformName(),
          })}
        </Text>
      </div>
    </Card>
  );
};

export default GeneratedSummary;
