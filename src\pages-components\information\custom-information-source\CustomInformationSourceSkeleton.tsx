import { Card } from "@/components/business/card";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * 自定义信息源页面骨架屏组件集合
 *
 * 这个文件包含了自定义信息源页面的所有骨架屏组件：
 *
 * 1. CustomInformationSourceSkeleton - 完整页面骨架屏
 *    - 包含标题区域、添加按钮和信息源列表
 *    - 支持配置信息源卡片数量
 *
 * 2. SourceCardSkeleton - 单个信息源卡片骨架屏
 *    - 模拟信息源卡片的结构
 *    - 包含名称、URL、删除按钮等元素
 *
 * 使用示例：
 * ```tsx
 * // 完整页面加载
 * <CustomInformationSourceSkeleton sourceCount={5} />
 *
 * // 单个卡片
 * <SourceCardSkeleton />
 * ```
 */

interface CustomInformationSourceSkeletonProps {
  /** 信息源卡片数量 */
  sourceCount?: number;
}

/**
 * 自定义信息源页面骨架屏组件
 * 包含标题区域、添加按钮和信息源列表的加载状态
 */
const CustomInformationSourceSkeleton = ({
  sourceCount = 3,
}: CustomInformationSourceSkeletonProps) => {
  return (
    <Card>
      {/* 标题区域骨架屏 */}
      <div className="mb-4 flex items-center justify-between">
        {/* 主标题骨架屏 */}
        <Skeleton className="h-7 w-40" />
        {/* 右侧区域：提示文本 + 添加按钮 */}
        <div className="flex items-center gap-2">
          {/* 提示文本骨架屏 */}
          <Skeleton className="h-4 w-32" />
          {/* 添加按钮骨架屏 */}
          <Skeleton className="h-8 w-20 rounded-md" />
        </div>
      </div>

      {/* 信息源列表骨架屏 */}
      <div className="grid gap-4">
        {Array.from({ length: sourceCount }).map((_, index) => (
          <SourceCardSkeleton key={index} />
        ))}
      </div>
    </Card>
  );
};

/**
 * 单个信息源卡片骨架屏组件
 * 模拟信息源卡片的结构和布局
 */
export const SourceCardSkeleton = () => {
  return (
    <Card className="bg-surface-light">
      <div className="flex justify-between items-center">
        {/* 左侧内容区域 */}
        <div className="flex-1 min-w-0">
          {/* 信息源名称骨架屏 */}
          <Skeleton className="h-5 w-32 mb-2" />
          {/* URL 骨架屏 */}
          <Skeleton className="h-4 w-48" />
        </div>

        {/* 右侧删除按钮骨架屏 */}
        <div className="ml-4 shrink-0">
          <Skeleton className="h-8 w-16 rounded-md" />
        </div>
      </div>
    </Card>
  );
};

export default CustomInformationSourceSkeleton;
