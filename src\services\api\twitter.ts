import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";

// twitter/auth
export const twitterAuthApi = async (
  taskId: string
): Promise<
  ApiResponse<{
    authUrl: string;
    state: string;
  }>
> => {
  return await api.post(`/twitter/auth`, { taskId });
};

// twitter/callback
export const twitterCallbackApi = async (data: {
  code: string;
}): Promise<ApiResponse<{ success: boolean }>> => {
  return await api.post(`/twitter/callback`, data);
};

// twitter/post
export const twitterPostApi = async (data: {
  text: string;
  images?: { s3Key: string }[];
}): Promise<ApiResponse<{ success: boolean }>> => {
  return await api.post(`/twitter/post`, data);
};

// twitter/status
export const twitterStatusApi = async (): Promise<
  ApiResponse<{
    isConnected: boolean;
    isExpired: boolean;
    twitterUser: {
      id: string;
      username: string;
      connectedAt: string;
    } | null;
  }>
> => {
  return await api.get(`/twitter/status`);
};
