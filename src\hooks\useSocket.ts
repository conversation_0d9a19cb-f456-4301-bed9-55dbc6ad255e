import { useCallback, useEffect, useRef, useState } from "react";

// 工具函数
const isValidJson = (str: string): boolean => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};

/* eslint-disable @typescript-eslint/no-explicit-any */

type UseWebSocketWithReconnectionProps = {
  onMessage?: (message: any) => void; // 可选的消息回调函数
};

const useWebSocketWithReconnection = ({
  onMessage,
}: UseWebSocketWithReconnectionProps) => {
  const socketRef = useRef<WebSocket | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionId, setConnectionId] = useState<string | null>(null); // 添加 connectionId 状态
  const reconnectAttempts = useRef(0);
  const lastPongTimestamp = useRef(Date.now());
  const inactivityTimeout = 30 * 60 * 1000; // 30 分钟
  const isManualClose = useRef(false); // 新增标记变量
  let inactivityTimer: ReturnType<typeof setTimeout>;

  const handleMessage = (event: MessageEvent) => {
    const message = JSON.parse(isValidJson(event.data) ? event.data : "{}");
    if (message.type === "pong") {
      lastPongTimestamp.current = Date.now();
    }
    // 如果消息包含 connectionId，则更新状态
    if (message.connectionId) {
      setConnectionId(message.connectionId);
    }
    if (onMessage) {
      onMessage(message);
    }
  };

  // 非主动关闭后 重连
  const handleClose = (_: CloseEvent) => {
    setIsConnected(false);
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  };

  const startWebSocket = useCallback(() => {
    // 如果已经有连接且状态正常，直接返回
    if (
      socketRef.current &&
      (socketRef.current.readyState === WebSocket.OPEN ||
        socketRef.current.readyState === WebSocket.CONNECTING)
    ) {
      console.log("WebSocket already connected or connecting, skipping...");
      return;
    }

    // 清理旧连接
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }

    const url = process.env.NEXT_PUBLIC_SOCKET!;
    socketRef.current = new WebSocket(url);
    isManualClose.current = false;

    socketRef.current.onopen = () => {
      console.log("WebSocket connected!");
      setIsConnected(true);
      reconnectAttempts.current = 0; // 重置重连次数
      lastPongTimestamp.current = Date.now();

      // 发送心跳的定时器
      heartbeatIntervalRef.current = setInterval(() => {
        if (socketRef.current?.readyState === WebSocket.OPEN) {
          socketRef.current.send(JSON.stringify({ type: "ping" }));
          console.log("send heartbeat!");
        }
      }, 3000);
    };

    // 绑定事件处理器
    socketRef.current.onmessage = handleMessage;
    socketRef.current.onclose = handleClose;
  }, []);

  // 监听用户活动，释放连接
  const handleUserActivity = () => {
    clearTimeout(inactivityTimer);
    inactivityTimer = setTimeout(() => {
      closeWebSocket();
    }, inactivityTimeout);
  };

  // 主动关闭
  const closeWebSocket = useCallback(() => {
    if (socketRef.current) {
      isManualClose.current = true; // 标记为主动关闭
      socketRef.current.close();
      socketRef.current = null;
      setIsConnected(false);
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
    }
  }, []);
  // 初始化 - 建立连接,
  useEffect(() => {
    // startWebSocket();

    // 监听用户活动以防止自动断开
    window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("keypress", handleUserActivity);

    // 清理资源
    return () => {
      window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("keypress", handleUserActivity);
      clearTimeout(inactivityTimer);

      if (socketRef.current) {
        socketRef.current.close(); // 关闭 WebSocket 连接
        socketRef.current = null;
      }
    };
  }, []);

  return {
    socket: socketRef.current,
    isConnected,
    connectionId, // 暴露 connectionId
    closeWebSocket,
    startWebSocket,
  };
};

export default useWebSocketWithReconnection;
