import { redirect } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { ReactNode } from "react";

interface ServerAuthGuardProps {
  children: ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

/**
 * 服务端认证守卫组件
 * 在服务端检查用户认证状态，避免客户端闪烁
 */
export default async function ServerAuthGuard({
  children,
  redirectTo = "/login",
  requireAuth = true,
}: ServerAuthGuardProps) {
  // 获取服务端 session
  const session = await getServerSession();

  // 如果需要认证但没有 session，重定向到登录页
  if (requireAuth && !session) {
    redirect(redirectTo);
  }

  // 如果不需要认证但有 session，可以重定向到主页（可选）
  if (!requireAuth && session && redirectTo !== "/login") {
    redirect("/");
  }

  return <>{children}</>;
}

/**
 * 用于页面级别的认证检查
 */
export async function withServerAuth<T extends Record<string, unknown>>(
  Component: React.ComponentType<T>,
  options: {
    redirectTo?: string;
    requireAuth?: boolean;
  } = {}
) {
  return async function AuthenticatedComponent(props: T) {
    const session = await getServerSession();
    const { redirectTo = "/login", requireAuth = true } = options;

    if (requireAuth && !session) {
      redirect(redirectTo);
    }

    if (!requireAuth && session) {
      redirect("/");
    }

    return <Component {...props} />;
  };
}

/**
 * 获取服务端认证状态的工具函数
 */
export async function getServerAuthStatus() {
  try {
    const session = await getServerSession();
    return {
      isAuthenticated: !!session,
      session,
      user: session?.user || null,
    };
  } catch (error) {
    console.error("Failed to get server auth status:", error);
    return {
      isAuthenticated: false,
      session: null,
      user: null,
    };
  }
}
