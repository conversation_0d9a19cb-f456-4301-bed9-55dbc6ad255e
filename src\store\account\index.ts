import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  signIn<PERSON>pi,
  IUser,
  getCurrentUser<PERSON>pi,
  googleSignIn<PERSON>pi,
  IGoogleLoginData,
  // syncSessionApi,
} from "@/services/api/user";
interface AccountStore {
  loginForm: {
    phone?: string;
    code?: string;
  };
  user: IUser;
  isLoading: boolean;
  setLoginForm: (form: AccountStore["loginForm"]) => void;
  login: () => Promise<string>;
  googleLogin: (googleData: IGoogleLoginData) => Promise<string>;
  syncSession: (sessionData: {
    token: string;
    userId: string;
    user: IUser;
  }) => Promise<void>;
  fetchCurrentUser: () => Promise<void>;
  logout: () => void;
}
export const useAccountStore = create<AccountStore>()(
  immer((set, get) => ({
    loginForm: {
      phone: "***********",
      code: "123456",
    },
    user: {} as IUser,
    isLoading: false,

    setLoginForm: (form: AccountStore["loginForm"]) => {
      set({
        loginForm: {
          ...get().loginForm,
          ...form,
        },
      });
    },

    // 手机号登录
    async login() {
      set({ isLoading: true });
      try {
        const { phone, code } = get().loginForm;
        const res = await signInApi({ phone, code });
        if (res.success && res.data?.token) {
          set({ user: res.data.user || ({} as IUser) });
          return res.data.token;
        } else {
          return "";
        }
      } finally {
        set({ isLoading: false });
      }
    },

    // 谷歌登录
    async googleLogin(googleData: IGoogleLoginData) {
      set({ isLoading: true });
      try {
        const res = await googleSignInApi(googleData);
        if (res.success && res.data?.token) {
          set({ user: res.data.user });
          return res.data.token;
        } else {
          return "";
        }
      } finally {
        set({ isLoading: false });
      }
    },

    // 同步NextAuth session
    async syncSession(sessionData: {
      token: string;
      userId: string;
      user: IUser;
    }) {
      try {
        // await syncSessionApi(sessionData);
        set({ user: sessionData.user });
        localStorage.setItem("token", sessionData.token);
      } catch (error) {
        console.error("Failed to sync session:", error);
      }
    },

    // 获取当前用户
    async fetchCurrentUser() {
      try {
        const res = await getCurrentUserApi();
        if (res.success && res.data) {
          set({ user: res.data.user });
        }
      } catch (error) {
        console.error("Failed to fetch current user:", error);
      }
    },

    // 登出
    logout() {
      set({
        user: {} as IUser,
        loginForm: { phone: "", code: "" },
      });
      localStorage.removeItem("token");
    },
  }))
);
