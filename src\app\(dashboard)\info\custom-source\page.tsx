import dynamic from "next/dynamic";
import { PreloadWrapper } from "@/components/PreloadWrapper";

const CustomInformationSource = dynamic(
  () => import("@/pages-components/information/custom-information-source"),
  {
    loading: () => <div>Loading...</div>,
  }
);

export default function CustomInformationSourcePage() {
  return (
    <PreloadWrapper routes={["/info/source", "/info/knowledge-base"]}>
      <CustomInformationSource />
    </PreloadWrapper>
  );
}
