"use client";

import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useLanguage } from "@/hooks/useLanguage";
import { IGenerateList } from "@/services/api/generate";
import useGenerateStore from "@/store/generate";
import useRoleStore from "@/store/persona";
import useTwitterStore from "@/store/twitter";

/**
 * 生成内容管理 Hook
 * 封装生成结果页面的业务逻辑
 */
export const useGeneratedContent = () => {
  const { currentRole } = useRoleStore();
  const { currentLanguage } = useLanguage();
  const {
    getResultByTaskId,
    finalContent,
    publish: publishToStore,
    save: saveToStore,
  } = useGenerateStore();
  const { statusToTwitter } = useTwitterStore();
  const searchParams = useSearchParams();

  // 获取当前任务ID
  const taskId = searchParams.get("taskId");

  // 获取生成结果
  useEffect(() => {
    if (taskId) {
      getResultByTaskId(taskId);
    }
  }, [taskId, getResultByTaskId]);

  // 发布处理函数
  const handlePublish = async (data: IGenerateList) => {
    if (!taskId) {
      throw new Error("Task ID is required for publishing");
    }
    if (currentLanguage !== "zh-CN") {
      await publishToStore(data, taskId, currentRole.personaId);
    } else {
      console.log("publish", "twitter");
      // TODO: 发布到 Twitter
      const isLoginTwitter = await statusToTwitter();
      // 先验证twitter登录状态
      // 如果登录了，直接发布
    }
  };

  // 保存处理函数
  const handleSave = async (data: IGenerateList) => {
    if (!taskId) {
      throw new Error("Task ID is required for saving");
    }
    await saveToStore(data, taskId, currentRole.personaId);
  };

  return {
    finalContent,
    taskId,
    handlePublish,
    handleSave,
  };
};
