import dynamic from "next/dynamic";
import { PreloadWrapper } from "@/components/PreloadWrapper";

const InformationSource = dynamic(
  () => import("@/pages-components/information/builtin-datasource"),
  {
    loading: () => <div>Loading...</div>,
  }
);

export default function InfoSourcePage() {
  return (
    <PreloadWrapper
      routes={["/camera-roll", "/info/custom-source", "/info/knowledge-base"]}
    >
      <InformationSource />
    </PreloadWrapper>
  );
}
