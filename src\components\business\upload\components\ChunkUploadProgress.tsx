import React from "react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Text, TextVariant } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import type { UploadProgress } from "../types";

interface ChunkUploadProgressProps {
  fileId: string;
  fileName: string;
  fileSize: number;
  progress: UploadProgress;
  onCancel?: (fileId: string) => void;
}

export const ChunkUploadProgress: React.FC<ChunkUploadProgressProps> = ({
  fileId,
  fileName,
  fileSize,
  progress,
  onCancel,
}) => {
  const { t } = useLanguage();
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) {
      return "0 Bytes";
    }

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "text-success";
      case "error":
        return "text-destructive";
      case "uploading":
        return "text-primary";
      case "cancelled":
        return "text-muted-foreground";
      default:
        return "text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return t("upload:status.pending");
      case "uploading":
        return t("upload:status.uploading");
      case "success":
        return t("upload:status.success");
      case "error":
        return t("upload:status.error");
      case "cancelled":
        return t("upload:status.cancelled");
      default:
        return t("upload:status.unknown");
    }
  };

  return (
    <div className="p-4 border border-border rounded-lg bg-surface">
      {/* 文件信息 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex-1 min-w-0">
          <Text
            variant={TextVariant.BODY_MEDIUM}
            className="font-medium truncate"
          >
            {fileName}
          </Text>
          <Text variant={TextVariant.CAPTION} className="text-text-tertiary">
            {formatFileSize(fileSize)}
          </Text>
        </div>

        <div className="flex items-center gap-2 ml-4">
          <Text
            variant={TextVariant.CAPTION}
            className={getStatusColor(progress.status)}
          >
            {getStatusText(progress.status)}
          </Text>

          {progress.status === "uploading" && onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCancel(fileId)}
              className="h-6 px-2 text-xs"
            >
              取消
            </Button>
          )}
        </div>
      </div>

      {/* 进度条 */}
      <div className="mb-2">
        <Progress value={progress.progress} className="h-2" />
      </div>

      {/* 进度信息 */}
      <div className="flex items-center justify-between text-xs text-text-tertiary">
        <span>{progress.progress}%</span>

        <div className="flex items-center gap-4">
          {progress.uploadSpeed && (
            <span>
              {t("upload:progress.speed")}: {progress.uploadSpeed}
            </span>
          )}

          {progress.remainingTime && progress.status === "uploading" && (
            <span>
              {t("upload:progress.remaining")}: {progress.remainingTime}
            </span>
          )}

          {progress.status === "success" && (
            <span className="text-success">
              ✓ {t("upload:progress.complete")}
            </span>
          )}

          {progress.status === "error" && (
            <span className="text-destructive">
              ✗ {t("upload:progress.failed")}
            </span>
          )}
        </div>
      </div>

      {/* 错误信息 */}
      {progress.status === "error" && (
        <div className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-xs text-destructive">
          {t("upload:errors.uploadFailed")}
        </div>
      )}
    </div>
  );
};

export default ChunkUploadProgress;
