# XHS - Content Generator

一个基于 Next.js 15 + React 19 + TypeScript 的现代化前端项目，灵感来源于小红书的内容生成平台。

## ✨ 特性

- 🚀 **现代技术栈**: Next.js 15, React 19, TypeScript
- 🌍 **国际化支持**: 支持中文、英文、日文三种语言
- 🎨 **主题系统**: 支持亮色/暗色主题切换，响应系统偏好
- 📱 **响应式设计**: 完整的移动端适配
- 🎯 **组件化架构**: 统一的设计系统和可复用组件
- 🔧 **开发体验**: 完整的 TypeScript 支持，ESLint 配置
- 🎭 **UI 组件库**: 基于 HeroUI 的组件系统
- 🎪 **动画效果**: 流畅的过渡动画和交互效果

## 🏗️ 项目结构

```
src/
├── app/                    # Next.js App Router
├── components/             # 组件目录
│   ├── ui/                # 基础 UI 组件
│   ├── business/          # 业务组件
│   └── ErrorBoundary.tsx  # 错误边界
├── hooks/                 # 自定义 Hooks
├── lib/                   # 工具库
├── utils/                 # 工具函数
├── types/                 # 类型定义
├── constants/             # 常量配置
├── config/                # 配置文件
├── locales/               # 国际化文件
└── provider/              # Context Providers
```

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm, yarn, pnpm 或 bun

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看结果。

### 构建生产版本

```bash
npm run build
npm run start
```

## 🎨 设计系统

### 主题配置

项目支持完整的主题系统，包括：

- **颜色系统**: 主色调、次要色、状态色等
- **字体系统**: 响应式字体大小和行高
- **间距系统**: 统一的间距规范
- **阴影系统**: 多层次的阴影效果
- **动画系统**: 流畅的过渡动画

### 组件库

提供了完整的 UI 组件库：

- **Text**: 统一的文本组件
- **Button**: 多种样式的按钮组件
- **Card**: 卡片容器组件
- **Modal**: 模态框组件
- **Toast**: 通知组件
- **Loading**: 加载状态组件

## 🌍 国际化

支持多语言切换：

- 中文 (zh-CN)
- 英文 (en-US)
- 日文 (ja-JP)

语言文件位于 `src/locales/` 目录下，按命名空间组织。

## 📱 响应式设计

项目采用移动优先的响应式设计：

- **断点系统**: xs, sm, md, lg, xl, 2xl
- **响应式工具**: 提供响应式值处理工具
- **容器查询**: 支持基于容器的响应式设计

## 🔧 开发工具

### 代码质量

- **ESLint**: 代码规范检查
- **TypeScript**: 类型安全
- **Prettier**: 代码格式化

### 运行检查

```bash
# 代码检查
npm run lint

# 类型检查
npx tsc --noEmit
```

## 📦 主要依赖

- **Next.js 15**: React 框架
- **React 19**: UI 库
- **TypeScript**: 类型系统
- **Tailwind CSS**: 样式框架
- **HeroUI**: UI 组件库
- **Framer Motion**: 动画库
- **react-i18next**: 国际化

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [HeroUI](https://heroui.com/) - UI 组件库
- [Framer Motion](https://www.framer.com/motion/) - 动画库
