{"addRole": {"title": "添加新身份", "roleName": "身份名称", "roleNamePlaceholder": "例如：小雅", "gender": "性别", "genderPlaceholder": "选择性别", "male": "男", "female": "女", "mbtiType": "MBTI类型", "mbtiPlaceholder": "选择MBTI类型", "mbtiOptions": {"ENFP": "ENFP - 竞选者", "INFP": "INFP - 调停者", "ENFJ": "ENFJ - 主人公", "INFJ": "INFJ - 提倡者", "ENTP": "ENTP - 辩论家", "INTP": "INTP - 逻辑学家", "ENTJ": "ENTJ - 指挥官", "INTJ": "INTJ - 建筑师", "ESFP": "ESFP - 娱乐家", "ISFP": "ISFP - 探险家", "ESFJ": "ESFJ - 执政官", "ISFJ": "ISFJ - 守护者", "ESTP": "ESTP - 企业家", "ISTP": "ISTP - 鉴赏家", "ESTJ": "ESTJ - 总经理", "ISTJ": "ISTJ - 物流师"}, "avatar": "头像表情", "avatarPlaceholder": "选择一个表情符号，例如：🌸", "personality": "性格特点", "personalityPlaceholder": "例如：活泼开朗，充满好奇心，热爱生活", "roleIntro": "角色介绍", "roleIntroPlaceholder": "详细描述这个角色的背景，兴趣爱好，创作倾向等...", "topicPreference": "擅长话题", "topicPlaceholder": "用逗号分隔，例如：日常生活,美食,旅行,心情分享", "validation": {"nameRequired": "请输入人设名称", "nameMinLength": "人设名称至少需要2个字符", "nameMaxLength": "人设名称不能超过20个字符", "genderRequired": "请选择性别", "mbtiRequired": "请选择MBTI类型", "personalityRequired": "请输入性格特点", "personalityMinLength": "性格特点描述至少需要5个字符", "introductionRequired": "请输入详细介绍", "introductionMinLength": "详细介绍至少需要10个字符"}}, "addSource": {"title": "添加自定义信息源", "sourceName": "信息源名称", "sourceNamePlaceholder": "例如：TechCrunch", "sourceType": "源类型", "sourceTypePlaceholder": "选择源类型", "rssType": "RSS Feed", "urlAddress": "URL地址", "websiteType": "网站URL", "apiType": "社交媒体账号", "urlPlaceholder": "https://example.com/feed", "contentType": "内容类别", "techNews": "科技资讯", "lifestyle": "生活方式", "fashion": "时尚美妆", "food": "美食旅行", "validation": {"sourceNameRequired": "请输入信息源名称", "sourceNameMinLength": "信息源名称至少需要2个字符", "sourceTypeRequired": "请选择源类型", "urlRequired": "请输入URL地址", "urlInvalid": "请输入有效的URL地址"}}, "imageModal": {"title": "图片预览", "toolbar": {"zoomOut": "缩小", "zoomIn": "放大", "rotateLeft": "逆时针旋转", "rotateRight": "顺时针旋转", "reset": "重置", "fullscreen": "全屏", "download": "下载", "close": "关闭"}, "shortcuts": {"title": "快捷键", "close": "关闭", "zoom": "缩放", "rotate": "旋转", "reset": "重置", "drag": "拖拽", "move": "移动", "wheel": "滚轮"}, "info": {"scale": "缩放", "rotation": "旋转"}}, "publishToPlatform": {"title": "发布到平台", "connectWithTwitter": "连接 X (Twitter):", "twitterX": "Twitter/X", "connectAccount": "连接账户", "cancel": "取消", "connectWithX": "连接 X"}}