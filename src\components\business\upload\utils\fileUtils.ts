import { FILE_CONFIGS } from "./fileConfig";

// 文件类型检测工具
export const getFileCategory = (file: File | { type: string }) => {
  for (const [category, config] of Object.entries(FILE_CONFIGS)) {
    if ((config.types as readonly string[]).includes(file.type)) {
      return category;
    }
  }
  return null;
};

// 格式化文件大小
export const formatFileSize = (bytes: number) => {
  if (bytes === 0) {
    return "0 B";
  }
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// 格式化时间
export const formatTime = (dateString: string) => {
  return dateString.split(" ")[1] || dateString;
};

// 获取所有支持的文件扩展名
export const getAllExtensions = () => {
  return [
    ...FILE_CONFIGS.image.extensions,
    ...FILE_CONFIGS.document.extensions,
  ];
};
