import { useState, useCallback, useRef } from "react";
import { useLanguage } from "@/hooks/useLanguage";
import {
  startUploadApi,
  getPresignUrlApi,
  getCompleteApi,
} from "@/services/api/upload";
import type {
  FileItem,
  UploadProgress,
  ChunkUploadConfig,
  UploadPart,
} from "../types";
import {
  splitFileIntoParts,
  uploadPartWithRetry,
  calculateUploadSpeed,
  estimateRemainingTime,
  CHUNK_SIZE,
  MAX_CONCURRENT_UPLOADS,
  MAX_RETRY_ATTEMPTS,
} from "../utils/chunkUpload";

export const useChunkUpload = (config?: ChunkUploadConfig) => {
  const { t } = useLanguage();
  const [uploadProgress, setUploadProgress] = useState<
    Record<string, UploadProgress>
  >({});
  const [uploading, setUploading] = useState(false);
  const uploadStartTimes = useRef<Record<string, number>>({});

  // 配置参数
  const chunkSize = config?.chunkSize || CHUNK_SIZE;
  const maxConcurrent = config?.maxConcurrent || MAX_CONCURRENT_UPLOADS;
  const maxRetries = config?.maxRetries || MAX_RETRY_ATTEMPTS;

  // 更新文件上传进度
  const updateFileProgress = useCallback(
    (
      fileId: string,
      progress: number,
      fileKey?: string | null,
      hasError: boolean = false,
      uploadedBytes?: number
    ) => {
      setUploadProgress((prev) => {
        const current = prev[fileId];
        const startTime = uploadStartTimes.current[fileId];

        let uploadSpeed: string | undefined;
        let remainingTime: string | undefined;

        if (uploadedBytes && startTime && progress > 0 && progress < 100) {
          uploadSpeed = calculateUploadSpeed(uploadedBytes, startTime);
          // 根据已上传字节数和进度百分比估算总大小
          const totalBytes = uploadedBytes / (progress / 100);
          remainingTime = estimateRemainingTime(
            uploadedBytes,
            totalBytes,
            startTime,
            t
          );
        }

        return {
          ...prev,
          [fileId]: {
            ...current,
            fileId,
            progress,
            uploadSpeed,
            remainingTime,
            fileKey: fileKey || current?.fileKey, // 保存fileKey
            status: hasError
              ? "error"
              : progress === 100
              ? "success"
              : progress > 0
              ? "uploading"
              : "pending",
          },
        };
      });
    },
    []
  );

  // 上传单个文件
  const uploadFile = useCallback(
    async (fileItem: FileItem) => {
      const { file, id, controller } = fileItem;

      if (!controller) {
        throw new Error("FileItem controller is not initialized.");
      }

      try {
        // 记录开始时间
        uploadStartTimes.current[id] = Date.now();

        // 1. 初始化分片上传
        const { name, type } = file;
        const startResponse = await startUploadApi({
          fileName: name,
          contentType: type,
        });

        if (!startResponse.success) {
          throw new Error("Failed to start upload");
        }

        if (!startResponse.data) {
          throw new Error("No data returned from start upload API");
        }

        const { uploadId, fileKey } = startResponse.data;

        // 2. 文件分片
        const parts = splitFileIntoParts(file, chunkSize);
        const uploadResults: UploadPart[] = [];

        // 3. 并发上传分片
        let activeUploads = 0;
        let currentIndex = 0;
        let uploadedBytes = 0;

        const uploadPart = async (partIndex: number) => {
          if (controller.signal.aborted) {
            return;
          }

          try {
            // 获取预签名 URL
            const presignResponse = await getPresignUrlApi({
              uploadId,
              fileKey,
              partNumber: partIndex + 1,
            });

            if (!presignResponse.success) {
              throw new Error("Failed to get presigned URL");
            }

            if (!presignResponse.data) {
              throw new Error("No data returned from presign URL API");
            }

            // 上传分片
            const etag = await uploadPartWithRetry(
              parts[partIndex],
              presignResponse.data.presignUrl,
              controller.signal,
              maxRetries
            );

            uploadResults.push({ PartNumber: partIndex + 1, ETag: etag });
            uploadedBytes += parts[partIndex].size;

            // 更新进度（90% 用于上传，10% 用于完成）
            const progress = Math.round(
              (uploadResults.length / parts.length) * 90
            );
            updateFileProgress(id, progress, fileKey, false, uploadedBytes);
          } catch (err: unknown) {
            if (err instanceof Error && err.name === "AbortError") {
              // 分片上传被取消
            } else {
              throw err;
            }
          } finally {
            activeUploads--;
            runNext();
          }
        };

        const runNext = () => {
          while (activeUploads < maxConcurrent && currentIndex < parts.length) {
            activeUploads++;
            uploadPart(currentIndex++);
          }

          if (currentIndex === parts.length && activeUploads === 0) {
            completeUpload();
          }
        };

        const completeUpload = async () => {
          if (controller.signal.aborted) {
            return;
          }

          try {
            // 按分片号排序
            uploadResults.sort((a, b) => a.PartNumber - b.PartNumber);

            // 等待一小段时间确保所有分片都已处理
            await new Promise((resolve) => setTimeout(resolve, 300));

            // 完成上传
            const completeResponse = await getCompleteApi({
              uploadId,
              fileKey,
              parts: uploadResults,
            });

            if (completeResponse.success) {
              // 上传成功，更新进度为100%
              updateFileProgress(id, 100, fileKey, false, file.size);
            } else {
              console.error(
                `Upload completion failed for file ${id}:`,
                completeResponse
              );
              throw new Error("Failed to complete multipart upload.");
            }
          } catch (error) {
            console.error("Complete upload error:", error);
            updateFileProgress(id, 0, null, true);
          }
        };

        // 开始上传
        runNext();
      } catch (error: unknown) {
        console.error("Upload error:", error);
        if (error instanceof Error && error.name !== "AbortError") {
          updateFileProgress(id, 0, null, true);
        }
      }
    },
    [chunkSize, maxConcurrent, maxRetries, updateFileProgress]
  );

  // 批量上传文件
  const uploadFiles = useCallback(
    async (files: FileItem[]) => {
      try {
        // 为每个文件创建 AbortController
        const fileItems = files.map((fileItem) => ({
          ...fileItem,
          controller: fileItem.controller || new AbortController(),
        }));

        // 先初始化进度状态，再设置 uploading 状态
        fileItems.forEach((item) => {
          setUploadProgress((prev) => ({
            ...prev,
            [item.id]: {
              fileId: item.id,
              progress: 0,
              status: "pending",
            },
          }));
        });

        // 设置上传状态
        setUploading(true);

        // 并发上传所有文件
        await Promise.allSettled(
          fileItems.map((fileItem) => uploadFile(fileItem))
        );
      } finally {
        setUploading(false);
      }
    },
    [uploadFile]
  );

  // 取消上传
  const cancelUpload = useCallback((fileId: string) => {
    setUploadProgress((prev) => {
      const current = prev[fileId];
      if (current) {
        return {
          ...prev,
          [fileId]: {
            ...current,
            status: "cancelled",
          },
        };
      }
      return prev;
    });
  }, []);

  // 清除进度记录
  const clearProgress = useCallback(() => {
    setUploadProgress({});
    uploadStartTimes.current = {};
  }, []);

  return {
    uploadProgress,
    uploading,
    uploadFile,
    uploadFiles,
    cancelUpload,
    clearProgress,
  };
};
