"use client";

import { ContentGridProps } from "../types";
import PlatformContentRenderer from "./PlatformContentRenderer";

/**
 * 内容网格组件
 * 负责渲染生成的内容列表，使用响应式网格布局
 */
const ContentGrid = ({
  finalContent,
  currentLanguage,
  onPublish,
  onSave,
}: ContentGridProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-[repeat(auto-fit,480px)] gap-4 justify-center">
      {finalContent.map((item, index) => (
        <PlatformContentRenderer
          key={index}
          data={item}
          currentLanguage={currentLanguage}
          onPublish={onPublish}
          onSave={onSave}
        />
      ))}
    </div>
  );
};

export default ContentGrid;
