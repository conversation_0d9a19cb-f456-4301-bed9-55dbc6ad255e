import { AlertCircle, CheckCircle, File } from "lucide-react";
import type { StatusIconProps } from "../types";

export const StatusIcon = ({ status }: StatusIconProps) => {
  switch (status) {
    case "uploading":
      return (
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-warning border-t-transparent" />
      );
    case "success":
      return <CheckCircle className="w-4 h-4 text-success" />;
    case "error":
      return <AlertCircle className="w-4 h-4 text-error" />;
    case "embedding":
      return <span className="spin360 text-[16px]">⏳</span>;
    default:
      return <File className="w-4 h-4 text-info" />;
  }
};
