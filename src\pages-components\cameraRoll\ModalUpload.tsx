import { DialogModal } from "@/components/business/modal";
import { ImageUpload } from "@/components/business/upload/ImageUpload";
import {
  FileData,
  UploadCompleteInfo,
} from "@/components/business/upload/types";
import { useLanguage } from "@/hooks/useLanguage";
const ModalUpload = ({
  open,
  close,
  handleComplete,
}: {
  open: boolean;
  close: () => void;
  handleComplete: (files: FileData[]) => Promise<void>;
}) => {
  const { t } = useLanguage();
  const onUploadComplete = async (info: UploadCompleteInfo) => {
    // 只有当所有文件都上传完成时才调用 handleComplete 和关闭弹窗
    if (info.totalCount === info.completedCount) {
      await handleComplete(info.files);
      setTimeout(() => {
        close();
      }, 0);
      // 在这里直接关闭弹窗，而不是依赖父组件的逻辑
    }
  };
  if (!open) {
    return null;
  }
  return (
    <DialogModal
      open={open}
      openChange={close}
      className="w-[40rem] bg-surface"
      title={t("camera:photoAnalyzer.uploadPhoto")}
    >
      <ImageUpload
        onUploadComplete={onUploadComplete}
        onFileRemove={(_file) => {
          // 处理文件删除逻辑
        }}
      />
    </DialogModal>
  );
};
export default ModalUpload;
