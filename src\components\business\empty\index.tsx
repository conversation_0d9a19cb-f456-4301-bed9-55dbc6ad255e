"use client";

import {
  InboxIcon,
  MagnifyingGlassIcon,
  DocumentIcon,
  PhotoIcon,
  ChatBubbleLeftIcon,
  BellIcon,
  ClockIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import React from "react";
import { Button } from "@/components/ui/button";
import { Text, Body } from "@/components/ui/Text";
import { useLanguage } from "@/hooks/useLanguage";
import { cn } from "@/lib/utils";
import { TextVariant } from "@/types";

// 空数据类型枚举
export enum EmptyType {
  NO_DATA = "noData",
  NO_CONTENT = "noContent",
  NO_RESULTS = "noResults",
  NO_FILES = "noFiles",
  NO_IMAGES = "noImages",
  NO_MESSAGES = "noMessages",
  NO_NOTIFICATIONS = "noNotifications",
  NO_HISTORY = "noHistory",
  NO_ROLES = "noRoles",
}

// 空数据组件属性接口
export interface EmptyStateProps {
  /** 空数据类型 */
  type?: EmptyType;
  /** 自定义标题 */
  title?: string;
  /** 自定义描述 */
  description?: string;
  /** 是否显示描述 */
  isDescription?: boolean;
  /** 自定义图标 */
  icon?: React.ReactNode;
  /** 图标大小 */
  iconSize?: "sm" | "md" | "lg";
  /** 操作按钮配置 */
  actions?: Array<{
    label?: string;
    onClick: () => void;
    variant?: "default" | "outline" | "ghost" | "secondary";
    icon?: React.ReactNode;
  }>;
  /** 是否显示默认操作按钮 */
  showDefaultActions?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 是否紧凑模式 */
  compact?: boolean;
}

// 图标映射
const iconMap = {
  [EmptyType.NO_DATA]: InboxIcon,
  [EmptyType.NO_CONTENT]: InboxIcon,
  [EmptyType.NO_RESULTS]: MagnifyingGlassIcon,
  [EmptyType.NO_FILES]: DocumentIcon,
  [EmptyType.NO_IMAGES]: PhotoIcon,
  [EmptyType.NO_MESSAGES]: ChatBubbleLeftIcon,
  [EmptyType.NO_NOTIFICATIONS]: BellIcon,
  [EmptyType.NO_HISTORY]: ClockIcon,
  [EmptyType.NO_ROLES]: UserIcon,
};

// 默认操作按钮映射
const defaultActionsMap = {
  [EmptyType.NO_DATA]: ["refresh"],
  [EmptyType.NO_CONTENT]: ["create", "refresh"],
  [EmptyType.NO_RESULTS]: ["clear", "retry"],
  [EmptyType.NO_FILES]: ["upload", "refresh"],
  [EmptyType.NO_IMAGES]: ["upload", "refresh"],
  [EmptyType.NO_MESSAGES]: ["refresh"],
  [EmptyType.NO_NOTIFICATIONS]: ["refresh"],
  [EmptyType.NO_HISTORY]: ["refresh"],
  [EmptyType.NO_ROLES]: ["create"],
};

const EmptyState: React.FC<EmptyStateProps> = ({
  type = EmptyType.NO_DATA,
  title,
  description,
  isDescription = true,
  icon,
  iconSize = "lg",
  actions = [],
  showDefaultActions = false,
  className,
  compact = false,
}) => {
  const { t } = useLanguage();

  // 获取标题和描述
  const displayTitle = title || t(`components:empty.${type}`);
  const displayDescription =
    description || t(`components:empty.description.${type}`);

  // 图标尺寸映射
  const iconSizeMap = {
    sm: "w-12 h-12",
    md: "w-16 h-16",
    lg: "w-20 h-20",
  };

  // 获取默认操作按钮
  const getDefaultActions = (): Array<{
    label: string;
    onClick: () => void;
    variant: "default" | "outline" | "ghost";
    icon?: React.ReactNode;
  }> => {
    if (!showDefaultActions) {
      return [];
    }

    const actionKeys = defaultActionsMap[type] || [];
    return actionKeys.map((actionKey) => ({
      label: t(`components:empty.actions.${actionKey}`),
      onClick: () => {
        // 默认操作处理，实际使用时应该传入具体的处理函数
      },
      variant: (actionKey === "create" || actionKey === "upload"
        ? "default"
        : "outline") as "default" | "outline" | "ghost",
    }));
  };

  const allActions = [...actions, ...getDefaultActions()];

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center text-center",
        compact ? "py-4 px-4" : "py-16 px-6",
        className
      )}
    >
      {/* 图标 */}
      <div
        className={cn(
          "flex items-center justify-center rounded-full bg-surface-light mb-6",
          iconSizeMap[iconSize],
          compact && "mb-4"
        )}
      >
        {React.isValidElement(icon)
          ? icon
          : React.createElement(iconMap[type], {
              className: cn(
                "text-text-tertiary",
                iconSize === "sm" && "w-6 h-6",
                iconSize === "md" && "w-8 h-8",
                iconSize === "lg" && "w-10 h-10"
              ),
            })}
      </div>

      {/* 标题 */}
      <Text
        variant={TextVariant.BODY_SMALL}
        className={cn("text-text-primary font-medium mb-2", compact && "mb-1")}
      >
        {displayTitle}
      </Text>

      {/* 描述 */}
      {isDescription && (
        <Body
          size="small"
          className={cn(
            "text-text-secondary !text-[12px] max-w-md leading-relaxed",
            compact ? "mb-3" : "mb-8"
          )}
        >
          {displayDescription}
        </Body>
      )}

      {/* 操作按钮 */}
      {allActions.length > 0 && (
        <div
          className={cn(
            "flex flex-wrap gap-3 justify-center",
            compact && "gap-2"
          )}
        >
          {allActions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || "outline"}
              size={compact ? "sm" : "default"}
              onClick={action.onClick}
              className="min-w-[80px]"
            >
              {action.icon && <span className="mr-2">{action.icon}</span>}
              {action.label}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};

export default EmptyState;
